using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using Ecommerce.Application.Extensions;
using Ecommerce.Domain.Aggregates.Identity;
using MediatR;
using Microsoft.AspNetCore.Identity;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;

namespace Ecommerce.Application.Features.Identity.Commands.UpdateAllUsersSystemFlag;

/// <summary>
/// 批量更新所有用户的系统用户标识命令处理器
/// </summary>
public class UpdateAllUsersSystemFlagCommandHandler : IRequestHandler<UpdateAllUsersSystemFlagCommand, UpdateAllUsersSystemFlagResponse>
{
    private readonly UserManager<ApplicationUser> _userManager;
    private readonly ILogger<UpdateAllUsersSystemFlagCommandHandler> _logger;

    public UpdateAllUsersSystemFlagCommandHandler(
        UserManager<ApplicationUser> userManager,
        ILogger<UpdateAllUsersSystemFlagCommandHandler> logger)
    {
        _userManager = userManager;
        _logger = logger;
    }

    public async Task<UpdateAllUsersSystemFlagResponse> Handle(UpdateAllUsersSystemFlagCommand request, CancellationToken cancellationToken)
    {
        var response = new UpdateAllUsersSystemFlagResponse();

        try
        {
            _logger.LogInformation("开始批量更新所有用户的系统用户标识，强制更新: {ForceUpdate}", request.ForceUpdate);

            // 获取所有用户
            var users = await _userManager.Users.ToListAsync(cancellationToken);
            response.TotalUsers = users.Count;

            _logger.LogInformation("找到 {UserCount} 个用户需要处理", users.Count);

            foreach (var user in users)
            {
                try
                {
                    // 获取用户当前角色
                    var roles = await _userManager.GetRolesAsync(user);
                    
                    // 计算应该的系统用户标识
                    var shouldBeSystemUser = await _userManager.IsSystemUserAsync(user);
                    var currentIsSystemUser = user.IsSystemUser;

                    var result = new UserUpdateResult
                    {
                        UserId = user.Id.ToString(),
                        UserName = user.UserName ?? "",
                        Email = user.Email ?? "",
                        OldIsSystemUser = currentIsSystemUser,
                        NewIsSystemUser = shouldBeSystemUser,
                        Roles = roles.ToList()
                    };

                    // 检查是否需要更新
                    if (!request.ForceUpdate && currentIsSystemUser == shouldBeSystemUser)
                    {
                        result.IsSuccessful = true;
                        result.ErrorMessage = "无需更新";
                        response.Results.Add(result);
                        continue;
                    }

                    // 更新用户的系统用户标识
                    var updateResult = await _userManager.UpdateSystemUserFlagAsync(user);
                    
                    if (updateResult.Succeeded)
                    {
                        result.IsSuccessful = true;
                        response.UpdatedUsers++;
                        
                        _logger.LogDebug("成功更新用户 {UserName}({UserId}) 的系统用户标识: {OldValue} -> {NewValue}",
                            user.UserName, user.Id, currentIsSystemUser, shouldBeSystemUser);
                    }
                    else
                    {
                        result.IsSuccessful = false;
                        result.ErrorMessage = string.Join(", ", updateResult.Errors.Select(e => e.Description));
                        response.FailedUsers++;
                        response.Errors.Add($"用户 {user.UserName}: {result.ErrorMessage}");
                        
                        _logger.LogWarning("更新用户 {UserName}({UserId}) 的系统用户标识失败: {Errors}",
                            user.UserName, user.Id, result.ErrorMessage);
                    }

                    response.Results.Add(result);
                }
                catch (Exception ex)
                {
                    var result = new UserUpdateResult
                    {
                        UserId = user.Id.ToString(),
                        UserName = user.UserName ?? "",
                        Email = user.Email ?? "",
                        OldIsSystemUser = user.IsSystemUser,
                        NewIsSystemUser = user.IsSystemUser,
                        IsSuccessful = false,
                        ErrorMessage = ex.Message
                    };

                    response.Results.Add(result);
                    response.FailedUsers++;
                    response.Errors.Add($"用户 {user.UserName}: {ex.Message}");
                    
                    _logger.LogError(ex, "处理用户 {UserName}({UserId}) 时发生异常", user.UserName, user.Id);
                }
            }

            response.IsSuccessful = response.FailedUsers == 0;

            _logger.LogInformation("批量更新完成，总用户数: {TotalUsers}, 成功更新: {UpdatedUsers}, 失败: {FailedUsers}",
                response.TotalUsers, response.UpdatedUsers, response.FailedUsers);

            return response;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "批量更新用户系统标识时发生错误");
            
            response.IsSuccessful = false;
            response.Errors.Add($"批量更新失败: {ex.Message}");
            
            return response;
        }
    }
}
