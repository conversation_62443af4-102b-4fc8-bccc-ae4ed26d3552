using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using Ecommerce.Application.Extensions;
using Ecommerce.Application.Interfaces.Cache;
using Ecommerce.Domain.Aggregates.Identity;
using MediatR;
using Microsoft.AspNetCore.Identity;
using Microsoft.Extensions.Logging;

namespace Ecommerce.Application.Features.Identity.Commands.UserRoles
{
    /// <summary>
    /// 更新用户角色命令处理器
    /// </summary>
    public class UpdateUserRolesCommandHandler : IRequestHandler<UpdateUserRolesCommand, bool>
    {
        private readonly UserManager<ApplicationUser> _userManager;
        private readonly RoleManager<ApplicationRole> _roleManager;
        private readonly IPermissionCacheInvalidator _cacheInvalidator;
        private readonly ILogger<UpdateUserRolesCommandHandler> _logger;

        public UpdateUserRolesCommandHandler(
            UserManager<ApplicationUser> userManager,
            RoleManager<ApplicationRole> roleManager,
            IPermissionCacheInvalidator cacheInvalidator,
            ILogger<UpdateUserRolesCommandHandler> logger)
        {
            _userManager = userManager;
            _roleManager = roleManager;
            _cacheInvalidator = cacheInvalidator;
            _logger = logger;
        }

        public async Task<bool> Handle(UpdateUserRolesCommand request, CancellationToken cancellationToken)
        {
            try
            {
                // 获取用户
                var user = await _userManager.FindByIdAsync(request.UserId.ToString());
                if (user == null)
                {
                    _logger.LogWarning("尝试更新不存在的用户角色: {UserId}", request.UserId);
                    return false;
                }

                // 获取用户当前角色
                var currentRoles = await _userManager.GetRolesAsync(user);

                // 获取要分配的角色名称
                var newRoleNames = new List<string>();
                foreach (var roleId in request.RoleIds)
                {
                    var role = await _roleManager.FindByIdAsync(roleId);
                    if (role != null && !string.IsNullOrEmpty(role.Name))
                    {
                        newRoleNames.Add(role.Name);
                    }
                    else
                    {
                        _logger.LogWarning("尝试分配不存在的角色: {RoleId}", roleId);
                    }
                }

                // 计算需要添加和移除的角色
                var rolesToAdd = newRoleNames.Except(currentRoles).ToList();
                var rolesToRemove = currentRoles.Except(newRoleNames).ToList();

                // 为安全起见，防止移除管理员角色（如果用户是唯一管理员）
                if (rolesToRemove.Contains("Administrator"))
                {
                    // 检查是否还有其他管理员
                    var adminUsers = await _userManager.GetUsersInRoleAsync("Administrator");
                    if (adminUsers.Count <= 1)
                    {
                        _logger.LogWarning("尝试移除唯一管理员的管理员角色: {UserId}", request.UserId);
                        rolesToRemove.Remove("Administrator");
                    }
                }

                // 批量移除角色
                if (rolesToRemove.Count > 0)
                {
                    var removeResult = await _userManager.RemoveFromRolesAsync(user, rolesToRemove);
                    if (!removeResult.Succeeded)
                    {
                        var errors = string.Join(", ", removeResult.Errors.Select(e => e.Description));
                        _logger.LogError("从用户 {UserName}({UserId}) 移除角色失败: {Errors}",
                            user.UserName, request.UserId, errors);
                        return false;
                    }
                }

                // 批量添加角色
                if (rolesToAdd.Count > 0)
                {
                    var addResult = await _userManager.AddToRolesAsync(user, rolesToAdd);
                    if (!addResult.Succeeded)
                    {
                        var errors = string.Join(", ", addResult.Errors.Select(e => e.Description));
                        _logger.LogError("为用户 {UserName}({UserId}) 添加角色失败: {Errors}",
                            user.UserName, request.UserId, errors);
                        return false;
                    }
                }

                // 更新用户的系统用户标识
                await _userManager.UpdateSystemUserFlagAsync(user);

                // 更新安全戳，使所有现有令牌失效
                await _userManager.UpdateSecurityStampAsync(user);

                // 使用户权限缓存失效
                await _cacheInvalidator.InvalidateUserPermissionsCacheAsync(request.UserId);

                _logger.LogInformation("成功更新用户 {UserName}({UserId}) 的角色, 添加了 {AddCount} 个角色, 移除了 {RemoveCount} 个角色",
                    user.UserName, request.UserId, rolesToAdd.Count, rolesToRemove.Count);
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "更新用户角色失败: {UserId}", request.UserId);
                return false;
            }
        }
    }
}
