-- 更新用户的系统用户标识
-- 根据用户角色自动设置 is_system_user 字段

-- 首先，将所有用户设置为非系统用户
UPDATE identity_users 
SET is_system_user = false;

-- 然后，将拥有系统角色的用户设置为系统用户
-- 系统角色包括：Administrator, Merchant, Support（不包括 Customer）
UPDATE identity_users 
SET is_system_user = true 
WHERE id IN (
    SELECT DISTINCT u.id
    FROM identity_users u
    INNER JOIN identity_user_roles ur ON u.id = ur.user_id
    INNER JOIN identity_roles r ON ur.role_id = r.id
    WHERE r.name IN ('Administrator', 'Merchant', 'Support')
);

-- 验证更新结果
-- 查看用户及其角色和系统用户标识
SELECT 
    u.id,
    u.user_name,
    u.email,
    u.is_system_user,
    STRING_AGG(r.name, ', ') as roles
FROM identity_users u
LEFT JOIN identity_user_roles ur ON u.id = ur.user_id
LEFT JOIN identity_roles r ON ur.role_id = r.id
GROUP BY u.id, u.user_name, u.email, u.is_system_user
ORDER BY u.is_system_user DESC, u.user_name;

-- 统计系统用户分布
SELECT 
    is_system_user,
    COUNT(*) as user_count
FROM identity_users
GROUP BY is_system_user;

-- 验证逻辑：检查是否有只有Customer角色但被标记为系统用户的情况
SELECT 
    u.id,
    u.user_name,
    u.email,
    u.is_system_user,
    STRING_AGG(r.name, ', ') as roles
FROM identity_users u
LEFT JOIN identity_user_roles ur ON u.id = ur.user_id
LEFT JOIN identity_roles r ON ur.role_id = r.id
WHERE u.is_system_user = true
GROUP BY u.id, u.user_name, u.email, u.is_system_user
HAVING STRING_AGG(r.name, ', ') = 'Customer' OR STRING_AGG(r.name, ', ') IS NULL;
