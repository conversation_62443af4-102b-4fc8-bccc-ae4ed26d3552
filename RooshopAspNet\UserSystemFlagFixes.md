# 用户系统标识修复总结

## 问题分析

### 1. 用户注册时 is_system_user 设置问题
**状态**: ✅ 已确认无问题
- 用户注册时 `IsSystemUser` 正确设置为 `false`
- `ApplicationUser.Create()` 方法中已正确初始化

### 2. 系统角色初始化问题
**状态**: ✅ 已修复
- **问题**: 所有角色（包括Customer）都被设置为系统角色
- **修复**: 更新 `IdentityDataInitializer.cs`，Customer角色设置为非系统角色

### 3. 缺少自动更新 is_system_user 逻辑
**状态**: ✅ 已修复
- **问题**: 分配/移除角色时没有自动更新用户的 `is_system_user` 字段
- **修复**: 在所有角色操作处理器中添加自动更新逻辑

## 修复内容

### 1. 系统角色初始化修复
**文件**: `Ecommerce.Infrastructure\Identity\Services\IdentityDataInitializer.cs`

```csharp
// 修复前：所有角色都是系统角色
role = ApplicationRole.Create(roleName, roleDescription, true);

// 修复后：根据角色类型设置
var rolesToInit = new List<(string Name, string Description, bool IsSystemRole, Func<List<string>> GetPermissions)>
{
    ("Administrator", "系统管理员", true, () => permissionProvider.GetAdministratorPermissions()),
    ("Customer", "普通客户", false, () => permissionProvider.GetCustomerPermissions()),  // Customer 不是系统角色
    ("Merchant", "商户", true, () => permissionProvider.GetMerchantPermissions()),
    ("Support", "客服人员", true, () => permissionProvider.GetSupportPermissions())
};
```

### 2. 角色操作时自动更新用户系统标识
**修改的文件**:
- `AssignRoleToUserCommandHandler.cs`
- `RemoveRoleFromUserCommandHandler.cs`
- `UpdateUserRolesCommandHandler.cs`
- `CreateUserCommandHandler.cs`
- `RegisterUserCommandHandler.cs`

**添加的逻辑**:
```csharp
// 更新用户的系统用户标识
await _userManager.UpdateSystemUserFlagAsync(user);
```

### 3. 批量更新现有用户系统标识
**新增功能**:
- `UpdateAllUsersSystemFlagCommand` - 批量更新命令
- `UpdateAllUsersSystemFlagCommandHandler` - 命令处理器
- API端点: `POST /api/v1/users/update-system-flags`

### 4. 数据库修复脚本
**文件**: 
- `UpdateRoleSystemFlags.sql` - 修复角色系统标识
- `UpdateUserSystemFlags.sql` - 修复用户系统标识

## 系统用户判断逻辑

### 角色分类
- **系统角色**: Administrator, Merchant, Support
- **非系统角色**: Customer

### 用户分类逻辑
```csharp
public static async Task<bool> IsSystemUserAsync(this UserManager<ApplicationUser> userManager, ApplicationUser user)
{
    var roles = await userManager.GetRolesAsync(user);
    
    // 如果用户没有任何角色，则不是系统用户
    if (!roles.Any())
        return false;

    // 如果用户只有Customer角色，则不是系统用户
    if (roles.Count == 1 && roles.Contains("Customer"))
        return false;

    // 如果用户有除Customer之外的其他角色，则是系统用户
    return roles.Any(role => role != "Customer");
}
```

## 使用方法

### 1. 运行数据库修复脚本
```sql
-- 1. 修复角色系统标识
\i UpdateRoleSystemFlags.sql

-- 2. 修复用户系统标识
\i UpdateUserSystemFlags.sql
```

### 2. 使用API批量更新
```bash
POST /api/v1/users/update-system-flags
Content-Type: application/json
Authorization: Bearer <token>

{
    "forceUpdate": false
}
```

### 3. 验证修复结果
```sql
-- 检查角色系统标识
SELECT name, is_system_role FROM identity_roles ORDER BY name;

-- 检查用户系统标识
SELECT 
    u.user_name,
    u.is_system_user,
    STRING_AGG(r.name, ', ') as roles
FROM identity_users u
LEFT JOIN identity_user_roles ur ON u.id = ur.user_id
LEFT JOIN identity_roles r ON ur.role_id = r.id
GROUP BY u.id, u.user_name, u.is_system_user
ORDER BY u.is_system_user DESC;
```

## 预期结果

### 角色系统标识
- Administrator: `is_system_role = true`
- Merchant: `is_system_role = true`
- Support: `is_system_role = true`
- Customer: `is_system_role = false`

### 用户系统标识
- 只有Customer角色的用户: `is_system_user = false`
- 有Administrator/Merchant/Support角色的用户: `is_system_user = true`
- 没有角色的用户: `is_system_user = false`

## 注意事项

1. **向后兼容**: 修复不会影响现有功能
2. **自动更新**: 今后所有角色操作都会自动更新用户系统标识
3. **权限控制**: 批量更新API需要用户编辑权限
4. **日志记录**: 所有操作都有详细的日志记录
