using System;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using Ecommerce.Application.Extensions;
using Ecommerce.Application.Interfaces.Cache;
using Ecommerce.Application.Interfaces.Identity;
using Ecommerce.Domain.Aggregates.Identity;
using MediatR;
using Microsoft.AspNetCore.Identity;
using Microsoft.Extensions.Logging;

namespace Ecommerce.Application.Features.Identity.Commands.UserRoles
{
    /// <summary>
    /// 为用户分配角色命令处理器
    /// </summary>
    public class AssignRoleToUserCommandHandler : IRequestHandler<AssignRoleToUserCommand, bool>
    {
        private readonly UserManager<ApplicationUser> _userManager;
        private readonly RoleManager<ApplicationRole> _roleManager;
        private readonly IPermissionCacheInvalidator _cacheInvalidator;
        private readonly ILogger<AssignRoleToUserCommandHandler> _logger;

        public AssignRoleToUserCommandHandler(
            UserManager<ApplicationUser> userManager,
            RoleManager<ApplicationRole> roleManager,
            IPermissionCacheInvalidator cacheInvalidator,
            ILogger<AssignRoleToUserCommandHandler> logger)
        {
            _userManager = userManager;
            _roleManager = roleManager;
            _cacheInvalidator = cacheInvalidator;
            _logger = logger;
        }

        public async Task<bool> Handle(AssignRoleToUserCommand request, CancellationToken cancellationToken)
        {
            try
            {
                // 获取用户
                var user = await _userManager.FindByIdAsync(request.UserId.ToString());
                if (user == null)
                {
                    _logger.LogWarning("尝试为不存在的用户分配角色: {UserId}", request.UserId);
                    return false;
                }

                // 获取角色
                var role = await _roleManager.FindByIdAsync(request.RoleId);
                if (role == null)
                {
                    _logger.LogWarning("尝试分配不存在的角色: {RoleId}", request.RoleId);
                    return false;
                }

                // 检查用户是否已有该角色
                if (role.Name != null && await _userManager.IsInRoleAsync(user, role.Name))
                {
                    // 用户已有该角色，无需分配
                    return true;
                }

                // 分配角色
                if (role.Name == null)
                {
                    _logger.LogError("角色名称为空: {RoleId}", request.RoleId);
                    return false;
                }

                var result = await _userManager.AddToRoleAsync(user, role.Name);
                if (!result.Succeeded)
                {
                    var errors = string.Join(", ", result.Errors.Select(e => e.Description));
                    _logger.LogError("为用户 {UserName}({UserId}) 分配角色 {RoleName}({RoleId}) 失败: {Errors}",
                        user.UserName, request.UserId, role.Name, request.RoleId, errors);
                    return false;
                }

                // 更新用户的系统用户标识
                await _userManager.UpdateSystemUserFlagAsync(user);

                // 更新安全戳，使所有现有令牌失效
                await _userManager.UpdateSecurityStampAsync(user);

                // 使用户权限缓存失效
                await _cacheInvalidator.InvalidateUserPermissionsCacheAsync(request.UserId);

                _logger.LogInformation("成功为用户 {UserName}({UserId}) 分配角色 {RoleName}({RoleId})",
                    user.UserName, request.UserId, role.Name, request.RoleId);
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "为用户分配角色失败: {UserId}, {RoleId}", request.UserId, request.RoleId);
                return false;
            }
        }
    }
}
