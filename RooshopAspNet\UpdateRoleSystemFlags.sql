-- 更新角色的系统角色标识
-- 将 Customer 角色设置为非系统角色，其他角色设置为系统角色

-- 更新 Customer 角色为非系统角色
UPDATE identity_roles 
SET is_system_role = false 
WHERE name = 'Customer';

-- 更新其他角色为系统角色
UPDATE identity_roles 
SET is_system_role = true 
WHERE name IN ('Administrator', 'Merchant', 'Support');

-- 验证更新结果
SELECT 
    id,
    name,
    description,
    is_system_role,
    created_at
FROM identity_roles
ORDER BY name;

-- 统计角色类型分布
SELECT 
    is_system_role,
    COUNT(*) as role_count
FROM identity_roles
GROUP BY is_system_role;
