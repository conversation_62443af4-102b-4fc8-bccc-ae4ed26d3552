using System;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using Ecommerce.Application.Extensions;
using Ecommerce.Application.Interfaces.Cache;
using Ecommerce.Domain.Aggregates.Identity;
using MediatR;
using Microsoft.AspNetCore.Identity;
using Microsoft.Extensions.Logging;

namespace Ecommerce.Application.Features.Identity.Commands.UserRoles
{
    /// <summary>
    /// 从用户移除角色命令处理器
    /// </summary>
    public class RemoveRoleFromUserCommandHandler : IRequestHandler<RemoveRoleFromUserCommand, bool>
    {
        private readonly UserManager<ApplicationUser> _userManager;
        private readonly RoleManager<ApplicationRole> _roleManager;
        private readonly IPermissionCacheInvalidator _cacheInvalidator;
        private readonly ILogger<RemoveRoleFromUserCommandHandler> _logger;

        public RemoveRoleFromUserCommandHandler(
            UserManager<ApplicationUser> userManager,
            RoleManager<ApplicationRole> roleManager,
            IPermissionCacheInvalidator cacheInvalidator,
            ILogger<RemoveRoleFromUserCommandHandler> logger)
        {
            _userManager = userManager;
            _roleManager = roleManager;
            _cacheInvalidator = cacheInvalidator;
            _logger = logger;
        }

        public async Task<bool> Handle(RemoveRoleFromUserCommand request, CancellationToken cancellationToken)
        {
            try
            {
                // 获取用户
                var user = await _userManager.FindByIdAsync(request.UserId.ToString());
                if (user == null)
                {
                    _logger.LogWarning("尝试从不存在的用户移除角色: {UserId}", request.UserId);
                    return false;
                }

                // 获取角色
                var role = await _roleManager.FindByIdAsync(request.RoleId);
                if (role == null)
                {
                    _logger.LogWarning("尝试移除不存在的角色: {RoleId}", request.RoleId);
                    return false;
                }

                // 检查用户是否有该角色
                if (role.Name == null || !await _userManager.IsInRoleAsync(user, role.Name))
                {
                    // 用户没有该角色，无需移除
                    return true;
                }

                // 移除角色
                if (role.Name == null)
                {
                    _logger.LogError("角色名称为空: {RoleId}", request.RoleId);
                    return false;
                }

                var result = await _userManager.RemoveFromRoleAsync(user, role.Name);
                if (!result.Succeeded)
                {
                    var errors = string.Join(", ", result.Errors.Select(e => e.Description));
                    _logger.LogError("从用户 {UserName}({UserId}) 移除角色 {RoleName}({RoleId}) 失败: {Errors}",
                        user.UserName, request.UserId, role.Name, request.RoleId, errors);
                    return false;
                }

                // 更新用户的系统用户标识
                await _userManager.UpdateSystemUserFlagAsync(user);

                // 更新安全戳，使所有现有令牌失效
                await _userManager.UpdateSecurityStampAsync(user);

                // 使用户权限缓存失效
                await _cacheInvalidator.InvalidateUserPermissionsCacheAsync(request.UserId);

                _logger.LogInformation("成功从用户 {UserName}({UserId}) 移除角色 {RoleName}({RoleId})",
                    user.UserName, request.UserId, role.Name, request.RoleId);
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "从用户移除角色失败: {UserId}, {RoleId}", request.UserId, request.RoleId);
                return false;
            }
        }
    }
}
