using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using Ecommerce.Application.Cache;
using Ecommerce.Application.Extensions;
using Ecommerce.Domain.Aggregates.Identity;
using MediatR;
using Microsoft.AspNetCore.Identity;
using Microsoft.Extensions.Logging;

namespace Ecommerce.Application.Features.Identity.Commands.CreateUser;

/// <summary>
/// 创建用户命令处理器
/// </summary>
public class CreateUserCommandHandler : IRequestHandler<CreateUserCommand, CreateUserResponse>
{
    private readonly UserManager<ApplicationUser> _userManager;
    private readonly RoleManager<ApplicationRole> _roleManager;
    private readonly ICacheService _cacheService;
    private readonly ILogger<CreateUserCommandHandler> _logger;

    /// <summary>
    /// 构造函数
    /// </summary>
    public CreateUserCommandHandler(
        UserManager<ApplicationUser> userManager,
        RoleManager<ApplicationRole> roleManager,
        ICacheService cacheService,
        ILogger<CreateUserCommandHandler> logger)
    {
        _userManager = userManager ?? throw new ArgumentNullException(nameof(userManager));
        _roleManager = roleManager ?? throw new ArgumentNullException(nameof(roleManager));
        _cacheService = cacheService ?? throw new ArgumentNullException(nameof(cacheService));
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
    }

    /// <summary>
    /// 处理创建用户命令
    /// </summary>
    public async Task<CreateUserResponse> Handle(CreateUserCommand request, CancellationToken cancellationToken)
    {
        try
        {
            _logger.LogInformation("开始处理创建用户命令: {UserName}, {Email}", request.UserName, request.Email);

            // 验证密码匹配
            if (request.Password != request.ConfirmPassword)
            {
                _logger.LogWarning("创建用户失败: 密码不匹配");
                return new CreateUserResponse
                {
                    IsSuccessful = false,
                    Errors = new[] { "密码和确认密码不匹配" }
                };
            }

            // 创建用户
            var user = ApplicationUser.Create(
                request.UserName,
                request.Email,
                request.FirstName,
                request.LastName);

            // 设置手机号（如果提供）
            if (!string.IsNullOrEmpty(request.PhoneNumber))
            {
                user.PhoneNumber = request.PhoneNumber;
            }

            // 设置用户状态
            if (!string.IsNullOrEmpty(request.Status))
            {
                switch (request.Status.ToLower())
                {
                    case "inactive":
                        user.LockoutEnabled = true;
                        break;
                    case "locked":
                        user.LockoutEnabled = true;
                        user.LockoutEnd = DateTimeOffset.UtcNow.AddYears(100); // 长期锁定
                        break;
                    default: // active
                        user.LockoutEnabled = false;
                        user.LockoutEnd = null;
                        break;
                }
            }

            // 如果不需要邮箱确认，则直接确认邮箱
            if (!request.RequireEmailConfirmation)
            {
                user.EmailConfirmed = true;
            }

            // 创建用户
            var result = await _userManager.CreateAsync(user, request.Password);
            if (!result.Succeeded)
            {
                var errors = result.Errors.Select(e => e.Description).ToArray();
                _logger.LogWarning("创建用户失败: {UserName}, {Email}, 错误: {Errors}",
                    request.UserName, request.Email, string.Join(", ", errors));
                return new CreateUserResponse
                {
                    IsSuccessful = false,
                    Errors = errors
                };
            }

            // 分配角色
            if (request.Roles != null && request.Roles.Any())
            {
                foreach (var role in request.Roles)
                {
                    // 检查角色是否存在
                    if (await _roleManager.RoleExistsAsync(role))
                    {
                        await _userManager.AddToRoleAsync(user, role);
                    }
                    else
                    {
                        _logger.LogWarning("角色不存在: {Role}", role);
                    }
                }
            }
            else
            {
                // 默认分配Customer角色
                if (await _roleManager.RoleExistsAsync("Customer"))
                {
                    await _userManager.AddToRoleAsync(user, "Customer");
                }
            }

            // 更新用户的系统用户标识
            await _userManager.UpdateSystemUserFlagAsync(user);

            // 获取分配的角色
            var assignedRoles = await _userManager.GetRolesAsync(user);

            // 清除用户列表缓存
            await _cacheService.RemoveByPatternAsync("*:Users:List*");

            _logger.LogInformation("成功创建用户: {UserId}, {UserName}, {Email}",
                user.Id, user.UserName, user.Email);

            return new CreateUserResponse
            {
                Id = user.Id.ToString(),
                UserName = user.UserName!,
                Email = user.Email!,
                PhoneNumber = user.PhoneNumber,
                FirstName = user.FirstName,
                LastName = user.LastName,
                CreatedAt = user.CreatedAt,
                Roles = assignedRoles.ToList(),
                IsSuccessful = true,
                RequiresEmailConfirmation = request.RequireEmailConfirmation
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "创建用户时发生错误: {UserName}, {Email}, 错误: {Message}",
                request.UserName, request.Email, ex.Message);
            return new CreateUserResponse
            {
                IsSuccessful = false,
                Errors = new[] { ex.Message }
            };
        }
    }
}
