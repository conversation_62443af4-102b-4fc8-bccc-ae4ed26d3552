using System;
using System.Linq;
using System.Security.Claims;
using System.Threading.Tasks;
using Ecommerce.Application.Interfaces.Identity;
using Ecommerce.Domain.Aggregates.Identity;
using Microsoft.AspNetCore.Identity;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using System.Collections.Generic;

namespace Ecommerce.Infrastructure.Identity.Services
{
    /// <summary>
    /// Identity数据初始化服务
    /// </summary>
    public class IdentityDataInitializer
    {
        /// <summary>
        /// 初始化角色和权限
        /// </summary>
        public static async Task SeedRolesAndPermissionsAsync(IServiceProvider serviceProvider)
        {
            using var scope = serviceProvider.CreateScope();
            var roleManager = scope.ServiceProvider.GetRequiredService<RoleManager<ApplicationRole>>();
            var permissionProvider = scope.ServiceProvider.GetRequiredService<IPermissionProvider>();
            var logger = scope.ServiceProvider.GetRequiredService<ILogger<IdentityDataInitializer>>();

            try
            {
                logger.LogInformation("开始初始化角色和权限...");

                // 定义角色和对应的权限获取方法
                var rolesToInit = new List<(string Name, string Description, bool IsSystemRole, Func<List<string>> GetPermissions)>
                {
                    ("Administrator", "系统管理员", true, () => permissionProvider.GetAdministratorPermissions()),
                    ("Customer", "普通客户", false, () => permissionProvider.GetCustomerPermissions()),  // Customer 不是系统角色
                    ("Merchant", "商户", true, () => permissionProvider.GetMerchantPermissions()),
                    ("Support", "客服人员", true, () => permissionProvider.GetSupportPermissions())
                };

                foreach (var (roleName, roleDescription, isSystemRole, getPermissions) in rolesToInit)
                {
                    logger.LogInformation("处理角色: {RoleName}", roleName);

                    // 检查角色是否存在
                    var role = await roleManager.FindByNameAsync(roleName);

                    // 如果角色不存在，创建角色
                    if (role == null)
                    {
                        role = ApplicationRole.Create(roleName, roleDescription, isSystemRole);
                        var createResult = await roleManager.CreateAsync(role);

                        if (createResult.Succeeded)
                        {
                            logger.LogInformation("成功创建角色: {RoleName}", roleName);
                        }
                        else
                        {
                            var errors = string.Join(", ", createResult.Errors.Select(e => e.Description));
                            logger.LogError("创建角色 {RoleName} 失败: {Errors}", roleName, errors);
                            continue;
                        }
                    }
                    else
                    {
                        logger.LogInformation("角色 {RoleName} 已存在，检查权限更新", roleName);
                    }

                    // 获取角色应该拥有的权限
                    var expectedPermissions = getPermissions();
                    logger.LogInformation("角色 {RoleName} 应该拥有 {PermissionCount} 个权限", roleName, expectedPermissions.Count);

                    // 获取角色当前拥有的权限
                    var currentClaims = await roleManager.GetClaimsAsync(role);
                    var currentPermissions = currentClaims
                        .Where(c => c.Type == "Permission")
                        .Select(c => c.Value)
                        .ToHashSet();

                    logger.LogInformation("角色 {RoleName} 当前拥有 {CurrentPermissionCount} 个权限", roleName, currentPermissions.Count);

                    // 🔥 特殊处理：对于Administrator角色，强制重新分配所有权限以确保权限值是最新的
                    if (roleName == "Administrator")
                    {
                        logger.LogInformation("检测到Administrator角色，执行强制权限更新...");

                        // 移除所有现有权限
                        foreach (var existingClaim in currentClaims.Where(c => c.Type == "Permission"))
                        {
                            await roleManager.RemoveClaimAsync(role, existingClaim);
                        }

                        // 重新添加所有权限
                        foreach (var permission in expectedPermissions)
                        {
                            var addResult = await roleManager.AddClaimAsync(role, new Claim("Permission", permission));
                            if (!addResult.Succeeded)
                            {
                                var errors = string.Join(", ", addResult.Errors.Select(e => e.Description));
                                logger.LogWarning("为Administrator角色添加权限 {Permission} 失败: {Errors}", permission, errors);
                            }
                        }

                        logger.LogInformation("Administrator角色权限强制更新完成，重新分配了 {PermissionCount} 个权限", expectedPermissions.Count);
                    }
                    else
                    {
                        // 其他角色使用增量更新逻辑
                        // 添加缺失的权限
                        var permissionsToAdd = expectedPermissions.Where(p => !currentPermissions.Contains(p)).ToList();
                        if (permissionsToAdd.Any())
                        {
                            logger.LogInformation("为角色 {RoleName} 添加 {AddCount} 个新权限", roleName, permissionsToAdd.Count);

                            foreach (var permission in permissionsToAdd)
                            {
                                var addResult = await roleManager.AddClaimAsync(role, new Claim("Permission", permission));
                                if (addResult.Succeeded)
                                {
                                    logger.LogDebug("成功为角色 {RoleName} 添加权限: {Permission}", roleName, permission);
                                }
                                else
                                {
                                    var errors = string.Join(", ", addResult.Errors.Select(e => e.Description));
                                    logger.LogWarning("为角色 {RoleName} 添加权限 {Permission} 失败: {Errors}", roleName, permission, errors);
                                }
                            }
                        }
                        else
                        {
                            logger.LogInformation("角色 {RoleName} 无需添加新权限", roleName);
                        }

                        // 移除多余的权限（可选：如果某些权限不再需要）
                        var validPermissions = permissionProvider.GetAllPermissions().ToHashSet();
                        var permissionsToRemove = currentPermissions.Where(p => !expectedPermissions.Contains(p) && validPermissions.Contains(p)).ToList();

                        if (permissionsToRemove.Any())
                        {
                            logger.LogInformation("从角色 {RoleName} 移除 {RemoveCount} 个多余权限", roleName, permissionsToRemove.Count);

                            foreach (var permission in permissionsToRemove)
                            {
                                var removeResult = await roleManager.RemoveClaimAsync(role, new Claim("Permission", permission));
                                if (removeResult.Succeeded)
                                {
                                    logger.LogDebug("成功从角色 {RoleName} 移除权限: {Permission}", roleName, permission);
                                }
                                else
                                {
                                    var errors = string.Join(", ", removeResult.Errors.Select(e => e.Description));
                                    logger.LogWarning("从角色 {RoleName} 移除权限 {Permission} 失败: {Errors}", roleName, permission, errors);
                                }
                            }
                        }

                        // 移除无效的权限（不在权限常量中定义的权限）
                        var invalidPermissions = currentPermissions.Where(p => !validPermissions.Contains(p)).ToList();
                        if (invalidPermissions.Any())
                        {
                            logger.LogWarning("从角色 {RoleName} 移除 {InvalidCount} 个无效权限", roleName, invalidPermissions.Count);

                            foreach (var permission in invalidPermissions)
                            {
                                var removeResult = await roleManager.RemoveClaimAsync(role, new Claim("Permission", permission));
                                if (removeResult.Succeeded)
                                {
                                    logger.LogDebug("成功从角色 {RoleName} 移除无效权限: {Permission}", roleName, permission);
                                }
                                else
                                {
                                    var errors = string.Join(", ", removeResult.Errors.Select(e => e.Description));
                                    logger.LogWarning("从角色 {RoleName} 移除无效权限 {Permission} 失败: {Errors}", roleName, permission, errors);
                                }
                            }
                        }
                    }

                    // 最终统计
                    var finalClaims = await roleManager.GetClaimsAsync(role);
                    var finalPermissionCount = finalClaims.Count(c => c.Type == "Permission");
                    logger.LogInformation("角色 {RoleName} 权限更新完成，最终拥有 {FinalPermissionCount} 个权限", roleName, finalPermissionCount);
                }

                logger.LogInformation("角色和权限初始化完成");
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "初始化角色和权限时发生错误");
                throw;
            }
        }

        /// <summary>
        /// 初始化默认管理员用户
        /// </summary>
        public static async Task SeedAdminUserAsync(IServiceProvider serviceProvider, string adminEmail, string adminPassword)
        {
            using var scope = serviceProvider.CreateScope();
            var userManager = scope.ServiceProvider.GetRequiredService<UserManager<ApplicationUser>>();
            var logger = scope.ServiceProvider.GetRequiredService<ILogger<IdentityDataInitializer>>();

            try
            {
                // 检查管理员用户是否存在
                var adminUser = await userManager.FindByEmailAsync(adminEmail);
                if (adminUser == null)
                {
                    // 创建管理员用户
                    adminUser = ApplicationUser.Create(
                        userName: adminEmail.Split('@')[0],
                        email: adminEmail,
                        firstName: "系统",
                        lastName: "管理员"
                    );

                    var result = await userManager.CreateAsync(adminUser, adminPassword);
                    if (result.Succeeded)
                    {
                        logger.LogInformation("成功创建管理员用户");

                        // 添加到管理员角色
                        await userManager.AddToRoleAsync(adminUser, "Administrator");
                        logger.LogInformation("成功将用户添加到管理员角色");

                        // 确认邮箱
                        var token = await userManager.GenerateEmailConfirmationTokenAsync(adminUser);
                        await userManager.ConfirmEmailAsync(adminUser, token);
                        logger.LogInformation("成功确认管理员邮箱");
                    }
                    else
                    {
                        var errors = string.Join(", ", result.Errors.Select(e => e.Description));
                        logger.LogError("创建管理员用户失败: {Errors}", errors);
                    }
                }
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "初始化管理员用户时发生错误");
                throw;
            }
        }

        /// <summary>
        /// 初始化测试用户
        /// </summary>
        public static async Task SeedTestUsersAsync(IServiceProvider serviceProvider)
        {
            using var scope = serviceProvider.CreateScope();
            var userManager = scope.ServiceProvider.GetRequiredService<UserManager<ApplicationUser>>();
            var logger = scope.ServiceProvider.GetRequiredService<ILogger<IdentityDataInitializer>>();

            try
            {
                // 创建客户用户
                await CreateUserIfNotExistsAsync(
                    userManager,
                    logger,
                    "<EMAIL>",
                    "Customer@123456",
                    "Customer",
                    "测试",
                    "客户"
                );

                // 创建商户用户
                await CreateUserIfNotExistsAsync(
                    userManager,
                    logger,
                    "<EMAIL>",
                    "Merchant@123456",
                    "Merchant",
                    "测试",
                    "商户"
                );

                // 创建客服用户
                await CreateUserIfNotExistsAsync(
                    userManager,
                    logger,
                    "<EMAIL>",
                    "Support@123456",
                    "Support",
                    "测试",
                    "客服"
                );
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "初始化测试用户时发生错误");
                throw;
            }
        }

        /// <summary>
        /// 创建用户（如果不存在）
        /// </summary>
        private static async Task CreateUserIfNotExistsAsync(
            UserManager<ApplicationUser> userManager,
            ILogger logger,
            string email,
            string password,
            string roleName,
            string firstName,
            string lastName)
        {
            // 检查用户是否存在
            var user = await userManager.FindByEmailAsync(email);
            if (user == null)
            {
                // 创建用户
                user = ApplicationUser.Create(
                    userName: email.Split('@')[0],
                    email: email,
                    firstName: firstName,
                    lastName: lastName
                );

                var result = await userManager.CreateAsync(user, password);
                if (result.Succeeded)
                {
                    logger.LogInformation("成功创建{Role}用户", roleName);

                    // 添加到角色
                    await userManager.AddToRoleAsync(user, roleName);
                    logger.LogInformation("成功将用户添加到{Role}角色", roleName);

                    // 确认邮箱
                    var token = await userManager.GenerateEmailConfirmationTokenAsync(user);
                    await userManager.ConfirmEmailAsync(user, token);
                    logger.LogInformation("成功确认{Role}用户邮箱", roleName);
                }
                else
                {
                    var errors = string.Join(", ", result.Errors.Select(e => e.Description));
                    logger.LogError("创建{Role}用户失败: {Errors}", roleName, errors);
                }
            }
        }
    }
}