# RooShop 管理端页面实现计划


## 技术栈

- **项目结构**: Turborepo Monorepo (支持管理端和客户端应用)
- **包管理工具**: pnpm (支持工作区)
- **前端框架**: [Next.js](https://nextjs.org/) (App Router)
- **管理端UI**:
  - shadcn/ui组件库（使用`add --all`命令一键安装所有组件）
  - 官方blocks快速构建（dashboard-01、login-04等）
  - 零CSS开发策略，完全基于组件组合实现设计
- **样式**: Tailwind CSS V4 (直接使用shadcn/ui预定义的主题系统)
- **状态管理**:
  - 管理端: 基于React Server Components和Client Components分离的状态管理策略
- **图表库**: 基于Recharts的图表组件，与shadcn/ui主题系统无缝集成
- **国际化**: next-intl (集中式翻译管理，各应用通过专用钩子访问翻译)
- **API调用**: 分层API集成策略
  - 共享API客户端: 基于Swagger文档生成类型安全的API客户端
  - 服务器组件数据获取: Server Components直接使用API客户端获取数据
  - 客户端交互: Server Actions + SWR（用于数据获取和缓存）
- **表单处理**: React Hook Form + Zod + shadcn/ui表单组件的组合方案
- **表格处理**:
  - **核心引擎**: TanStack Table v8 (强大的表格状态管理)
  - **UI组件**: 基于 [shadcn-table](https://github.com/shadmann7/shadcn-table) 的完整表格组件系统
  - **功能特性**: 服务端排序、过滤、分页、列固定、高级筛选、批量操作
  - **组件架构**: 15个核心组件 + 8个业务组件 + 配置文件
  - **位置**: `apps/admin/src/components/tables/`
- **认证策略**: 基于shadcn的login-04 block + JWT认证方案
- **Icon库**: Lucide Icons (与shadcn/ui默认集成)
- **命令面板**: shadcn/ui command组件 (支持Cmd/Ctrl+K全局搜索)

## 📋 项目现状

### ✅ 已完成的基础设施

**API层**
- ✅ **39个API服务文件**已生成，覆盖所有业务模块
- ✅ 位置：`packages/core/src/api/generated/services/`
- ✅ 基于OpenAPI自动生成，类型安全
- ✅ 包含完整的CRUD操作和业务逻辑接口
- ✅ 包含认证拦截器、错误处理、token刷新机制

**数据获取层**
- ✅ `useApiQuery` - 基础数据查询hook
- ✅ `useApiMutation` - 数据变更操作hook
- ✅ `usePagedQuery` - 分页数据查询hook
- ✅ `useInfiniteQuery` - 无限滚动hook
- ✅ `useFilterState` - 过滤器状态管理hook
- ✅ `serverFetch` - 服务器端数据获取
- ✅ `createServerDataService` - 领域特定数据服务
- ✅ 位置：`packages/core/src/data/hooks.ts`

**缓存和错误处理**
- ✅ `CACHE_TAGS` - 完整缓存标签系统
- ✅ `revalidateData` - 智能缓存失效
- ✅ `ApiError` - 统一错误处理类
- ✅ `formatErrorMessage` - 错误格式化工具
- ✅ 请求拦截器：自动token注入、错误处理、日志记录
- ✅ 位置：`packages/core/src/data/server.ts` 和 `packages/core/src/api/errors.ts`

**表格组件系统 (新增)**
- ✅ **完整的表格组件库**：基于shadcn-table项目适配
- ✅ **15个核心组件**：
  - `DataTable` - 主表格组件（支持列固定、行选择）
  - `DataTableToolbar` - 智能工具栏（自动过滤器渲染）
  - `DataTablePagination` - 高级分页组件
  - `DataTableColumnHeader` - 可排序列头
  - `DataTableFacetedFilter` - 多选过滤器
  - `DataTableViewOptions` - 列可见性控制
  - `DataTableActionBar` - 批量操作栏
  - `DataTableDateFilter` - 日期范围过滤器
  - `DataTableSliderFilter` - 数值滑块过滤器
  - `DataTableFilterMenu` - Notion/Airtable风格高级过滤
  - `DataTableSkeleton` - 加载骨架屏
  - 其他高级过滤和排序组件
- ✅ **业务组件示例**：完整的TasksTable实现参考
- ✅ **类型系统**：完整的TypeScript类型定义和元数据支持
- ✅ **中文化界面**：所有组件已本地化
- ✅ **统一导出**：`@app/components/tables` 模块化导入
- ✅ 位置：`apps/admin/src/components/tables/`

**国际化基础设施**
- ✅ 完整的i18n配置和中间件
- ✅ 基础翻译文件：`common.json`, `menu.json`, `permissions.json`等
- ✅ `useAdminTranslations` hook已就绪
- ✅ 支持中英文切换
- ✅ 菜单翻译包含所有40个页面项目
- ✅ 位置：`packages/core/src/i18n/locales/admin/`

**权限系统**
- ✅ 动态菜单加载与权限过滤
- ✅ `withPermission`、`PermissionGuard`、`withMultiplePermissions`组件
- ✅ 菜单配置文件包含所有40个页面的权限定义
- ✅ 权限管理hooks：`useRoles`, `useRolePermissions`, `useUpdateRolePermissions`
- ✅ 位置：`packages/core/src/auth/`

**布局系统**
- ✅ **应用级布局**：`src/app/[locale]/(admin)/layout.tsx`
  - 认证检查和重定向、头部（含标题和面包屑）、侧边栏、头部导航、命令菜单、基础容器和间距、内容区域

**UI组件库**
- ✅ 完整的shadcn/ui组件库
- ✅ DataTable、Button、Card、Badge等核心组件
- ✅ Breadcrumb、Separator、Sidebar等布局组件
- ✅ 位置：`apps/admin/src/components/ui/`

## 🏗️ 架构规范（AI开发模式）

### **📁 目录结构标准**

```
rooshopweb/apps/admin/src/
├── app/[locale]/(admin)/           # 📍 路由入口层
│   ├── dashboard/page.tsx         # 简单页面：直接实现
│   └── user/
│       ├── list/page.tsx          # 复杂页面：导入业务组件
│       ├── role/page.tsx          # 导入业务组件
│       └── permission/page.tsx    # 导入业务组件
└── components/
    ├── layout/
    │   └── PageTemplate.tsx       # 标准页面模板
    ├── tables/                     # 💎 表格组件系统
    │   ├── core/                  # 15个核心表格组件
    │   │   ├── data-table.tsx     # 主表格组件
    │   │   ├── data-table-toolbar.tsx # 智能工具栏
    │   │   ├── data-table-pagination.tsx # 分页组件
    │   │   └── ...                # 其他核心组件
    │   ├── business/              # 业务表格组件
    │   │   ├── tasks-table.tsx    # 任务表格示例
    │   │   └── ...                # 其他业务表格
    │   ├── config/                # 配置和类型
    │   │   ├── data-table-types.ts # 类型定义
    │   │   └── ...                # 其他配置
    │   └── index.ts               # 统一导出
    ├── pages/                      # 💼 业务逻辑层
    │   └── user/
    │       ├── UserList.tsx       # 完整业务组件
    │       ├── RoleList.tsx       # 完整业务组件
    │       └── PermissionList.tsx # 完整业务组件
    └── ui/                         # 基础UI组件
```

### **🎯 开发模式选择**

#### **简单页面（<50行）**
```typescript
// src/app/[locale]/(admin)/dashboard/page.tsx
export default function DashboardPage() {
  return (
    <>
      <SectionCards />
      <ChartAreaInteractive />
      <DataTable data={data} />
    </>
  )
}
```

#### **复杂页面（>50行）**
```typescript
// src/app/[locale]/(admin)/user/list/page.tsx
import UserList from '@app/components/pages/user/UserList'
export default function UserListPage() {
  return <UserList />
}

// src/components/pages/user/UserList.tsx
export default withPermission(UserList, "Permissions.Users.View")
```

### **🤖 AI开发工作流**

#### **单次完成模式**
每次AI对话完成一个完整页面的开发，包括：

1. **创建路由文件**：`src/app/[locale]/(admin)/module/page-name/page.tsx`
2. **创建业务组件**：`src/components/pages/module/PageName.tsx`
3. **创建翻译文件**：`packages/core/src/i18n/locales/admin/cn/module.json`
4. **完整功能实现**：CRUD操作、权限控制、错误处理、国际化

#### **开发要求**
- ✅ **功能完整**：一次性实现所有CRUD功能
- ✅ **权限集成**：使用 `withPermission` 和 `PermissionGuard`
- ✅ **错误处理**：使用 `formatErrorMessage` 和 toast
- ✅ **国际化**：创建对应翻译文件
- ✅ **类型安全**：使用生成的API类型
- ✅ **表格功能**：使用新的表格组件系统

## 🛠️ AI开发模板

### **完整页面开发示例**

```typescript
// 1. 路由入口: src/app/[locale]/(admin)/user/list/page.tsx
import UserList from '@app/components/pages/user/UserList'
export default function UserListPage() {
  return <UserList />
}

// 2. 业务组件: src/components/pages/user/UserList.tsx
'use client';

import { useState, useMemo } from 'react';
import { usePagedQuery, useApiMutation } from '@rooshop/core/data';
import { useAdminTranslations } from '@rooshop/core/i18n';
import { withPermission, PermissionGuard } from '@rooshop/core/auth';
import { formatErrorMessage } from '@rooshop/core/api';
import { PageTemplate } from '@app/components/layout/PageTemplate';
import {
  DataTable,
  DataTableToolbar,
  DataTableColumnHeader,
  DataTablePagination,
  DataTableFacetedFilter,
  type Option
} from '@app/components/tables';
import { Button } from '@app/components/ui/button';
import { Checkbox } from '@app/components/ui/checkbox';
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from '@app/components/ui/dropdown-menu';
import { Plus, MoreVertical, Edit, Trash } from 'lucide-react';
import { toast } from 'sonner';
import { useReactTable, getCoreRowModel, getSortedRowModel, getFilteredRowModel, getPaginationRowModel } from '@tanstack/react-table';

function UserList() {
  const [createDialogOpen, setCreateDialogOpen] = useState(false);
  const t = useAdminTranslations('admin.cn.user');

  const { data, isLoading, handlePageChange } = usePagedQuery('/users');
  const createUser = useApiMutation('/users', { method: 'POST' });
  const deleteUser = useApiMutation('/users', { method: 'DELETE' });

  // 定义表格列
  const columns = useMemo(() => [
    {
      id: 'select',
      header: ({ table }) => (
        <Checkbox
          checked={table.getIsAllPageRowsSelected()}
          onCheckedChange={table.toggleAllPageRowsSelected}
          aria-label="选择全部"
        />
      ),
      cell: ({ row }) => (
        <Checkbox
          checked={row.getIsSelected()}
          onCheckedChange={row.toggleSelected}
          aria-label="选择行"
        />
      ),
      enableSorting: false,
      enableHiding: false,
    },
    {
      accessorKey: 'name',
      header: ({ column }) => (
        <DataTableColumnHeader column={column} title={t('name')} />
      ),
      meta: {
        label: t('name'),
        variant: 'text',
        placeholder: '搜索姓名...',
      },
      enableColumnFilter: true,
    },
    {
      accessorKey: 'email',
      header: ({ column }) => (
        <DataTableColumnHeader column={column} title={t('email')} />
      ),
      meta: {
        label: t('email'),
        variant: 'text',
        placeholder: '搜索邮箱...',
      },
      enableColumnFilter: true,
    },
    {
      accessorKey: 'status',
      header: ({ column }) => (
        <DataTableColumnHeader column={column} title={t('status')} />
      ),
      cell: ({ row }) => (
        <Badge variant={row.original.status === 'active' ? 'default' : 'secondary'}>
          {t(`status.${row.original.status}`)}
        </Badge>
      ),
      meta: {
        label: t('status'),
        variant: 'multiSelect',
        options: [
          { label: t('status.active'), value: 'active' },
          { label: t('status.inactive'), value: 'inactive' },
          { label: t('status.pending'), value: 'pending' },
        ],
      },
      enableColumnFilter: true,
    },
    {
      id: 'actions',
      header: t('actions'),
      cell: ({ row }) => (
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button variant="ghost" className="h-8 w-8 p-0">
              <MoreVertical className="h-4 w-4" />
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end">
            <PermissionGuard permissions={["Permissions.Users.EditUsers"]}>
              <DropdownMenuItem onClick={() => handleEdit(row.original)}>
                <Edit className="h-4 w-4 mr-2" />
                {t('edit')}
              </DropdownMenuItem>
            </PermissionGuard>
            <PermissionGuard permissions={["Permissions.Users.Delete"]}>
              <DropdownMenuItem
                onClick={() => handleDelete(row.original.id)}
                className="text-destructive"
              >
                <Trash className="h-4 w-4 mr-2" />
                {t('delete')}
              </DropdownMenuItem>
            </PermissionGuard>
          </DropdownMenuContent>
        </DropdownMenu>
      ),
      enableSorting: false,
      enableHiding: false,
    }
  ], [t]);

  // 初始化表格
  const table = useReactTable({
    data: data?.items || [],
    columns,
    getCoreRowModel: getCoreRowModel(),
    getSortedRowModel: getSortedRowModel(),
    getFilteredRowModel: getFilteredRowModel(),
    getPaginationRowModel: getPaginationRowModel(),
    enableRowSelection: true,
    getRowId: (row) => row.id,
  });

  const handleCreate = async (userData: any) => {
    try {
      await createUser.trigger(userData);
      toast.success(t('createSuccess'));
      setCreateDialogOpen(false);
    } catch (error) {
      toast.error(formatErrorMessage(error));
    }
  };

  const handleDelete = async (id: string) => {
    try {
      await deleteUser.trigger({ id });
      toast.success(t('deleteSuccess'));
    } catch (error) {
      toast.error(formatErrorMessage(error));
    }
  };

  return (
    <PageTemplate
      title={t('title')}
      description={t('description')}
      breadcrumb={[
        { title: '首页', href: '/dashboard' },
        { title: '用户管理', href: '/user' },
        { title: '用户列表' }
      ]}
      actions={
        <PermissionGuard permissions={["Permissions.Users.Create"]}>
          <Button onClick={() => setCreateDialogOpen(true)}>
            <Plus className="h-4 w-4 mr-2" />
            {t('create')}
          </Button>
        </PermissionGuard>
      }
      loading={isLoading}
    >
      <DataTable table={table}>
        <DataTableToolbar table={table} />
      </DataTable>

      {createDialogOpen && (
        <CreateUserDialog
          open={createDialogOpen}
          onClose={() => setCreateDialogOpen(false)}
          onSuccess={handleCreate}
        />
      )}
    </PageTemplate>
  );
}

export default withPermission(UserList, "Permissions.Users.View");

// 3. 翻译文件: packages/core/src/i18n/locales/admin/cn/user.json
{
  "title": "用户管理",
  "description": "管理系统用户，查看用户信息，分配角色",
  "create": "创建用户",
  "edit": "编辑用户",
  "delete": "删除用户",
  "createSuccess": "用户创建成功",
  "deleteSuccess": "用户删除成功",
  "name": "姓名",
  "email": "邮箱",
  "status": "状态",
  "actions": "操作",
  "status.active": "活跃",
  "status.inactive": "非活跃",
  "status.pending": "待审核"
}
```

## 🛠️ 技术参考

### 数据获取
```typescript
import { usePagedQuery, useApiMutation } from '@rooshop/core/data';

// 分页查询
const { data, isLoading, handlePageChange } = usePagedQuery('/endpoint');

// 数据变更
const mutation = useApiMutation('/endpoint', { method: 'POST' });
await mutation.trigger(data);
```

### 表格组件使用
```typescript
import {
  DataTable,
  DataTableToolbar,
  DataTableColumnHeader,
  DataTablePagination,
  DataTableFacetedFilter,
  type Option
} from '@app/components/tables';
import { useReactTable, getCoreRowModel } from '@tanstack/react-table';

// 列定义示例
const columns = [
  {
    accessorKey: 'name',
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="名称" />
    ),
    meta: {
      label: '名称',
      variant: 'text', // text | number | date | dateRange | select | multiSelect | range
      placeholder: '搜索名称...',
      options: [], // for select/multiSelect
      range: [0, 100], // for range/slider
    },
    enableColumnFilter: true,
  }
];

// 表格初始化
const table = useReactTable({
  data,
  columns,
  getCoreRowModel: getCoreRowModel(),
  getSortedRowModel: getSortedRowModel(),
  getFilteredRowModel: getFilteredRowModel(),
  getPaginationRowModel: getPaginationRowModel(),
});

// 使用组件
<DataTable table={table}>
  <DataTableToolbar table={table} />
</DataTable>
```

### 权限控制
```typescript
import { withPermission, PermissionGuard } from '@rooshop/core/auth';

// 页面级权限
export default withPermission(Component, "Permissions.Required");

// 组件级权限
<PermissionGuard permissions={["Permissions.Action"]}>
  <Button>操作</Button>
</PermissionGuard>
```

### 错误处理
```typescript
import { formatErrorMessage } from '@rooshop/core/api';
import { toast } from 'sonner';

try {
  await mutation.trigger(data);
  toast.success('操作成功');
} catch (error) {
  toast.error(formatErrorMessage(error));
}
```

### 国际化
```typescript
import { useAdminTranslations } from '@rooshop/core/i18n';

const t = useAdminTranslations('admin.cn.moduleName');
```

## 📝 页面开发清单

### 当前状态 (6/40 页面已完成)
- ✅ Dashboard (仪表盘)
- ✅ User Role Management (角色管理) - 已集成新表格系统
- ✅ User Permission Management (权限管理) - 已集成新表格系统
- ✅ User List (用户列表) - 开发中模板
- ✅ Category List (分类管理) - 开发中模板
- ✅ Product List (商品列表) - 开发中模板

### 待开发页面 (34/40)

#### 用户与权限管理模块
- [ ] `/user/userlist` - 用户列表 (依据表格组件系统重新实现)
- [ ] `/user/rolelist` - 角色管理 (依据表格组件系统重新实现)
- [ ] `/user/PermissionList.tsx` - 权限组管理 (依据表格组件系统重新实现)
- [ ] `/user/permission-groups` - 权限组管理
- [ ] `/user/permission-groups` - 权限组管理
- [ ] `/user/address` - 用户地址管理
- [ ] `/user/user-roles` - 用户角色管理
- [ ] `/role/inheritance` - 角色继承管理

#### 商品管理模块
- [ ] `/product/attribute` - 属性管理
- [ ] `/product/pending` - 待审核商品

#### 交易管理模块
- [ ] `/transaction/order` - 订单列表
- [ ] `/transaction/cart` - 购物车列表
- [ ] `/transaction/abandoned` - 未结购物车

#### 库存与物流模块
- [ ] `/inventory-logistics/inventory` - 库存列表
- [ ] `/inventory-logistics/alerts` - 库存预警
- [ ] `/inventory-logistics/shipping` - 物流列表
- [ ] `/inventory-logistics/area` - 配送区域
- [ ] `/inventory-logistics/rules` - 配送规则

#### 财务管理模块
- [ ] `/finance/payment` - 支付列表
- [ ] `/finance/refund` - 退款管理
- [ ] `/finance/invoice` - 发票列表
- [ ] `/finance/tax` - 税务设置

#### 营销中心模块
- [ ] `/marketing/coupon` - 优惠券管理
- [ ] `/marketing/promotion` - 促销活动
- [ ] `/marketing/contents` - 营销内容
- [ ] `/marketing/user-coupons` - 用户优惠券

#### 商户管理模块
- [ ] `/merchant/list` - 商户列表

#### 售后服务模块
- [ ] `/after-sales/return` - 退货列表
- [ ] `/after-sales/review` - 评价列表
- [ ] `/after-sales/ticket` - 工单列表
- [ ] `/after-sales/category` - 工单分类

#### 数据分析模块
- [ ] `/data/sales` - 销售概览
- [ ] `/data/inventory` - 库存概览
- [ ] `/data/user` - 用户统计
- [ ] `/data/topselling` - 热销商品

#### 系统管理模块
- [ ] `/system/settings` - 系统设置
- [ ] `/system/cache` - 缓存管理
- [ ] `/system/logs` - 日志管理
- [ ] `/system/api-metrics` - API指标监控
- [ ] `/system/health-check` - 健康检查
- [ ] `/system/translations` - 翻译管理
- [ ] `/system/search` - 搜索配置
- [ ] `/system/notification` - 通知列表
- [ ] `/system/notification-settings` - 通知设置
- [ ] `/system/webhook` - Webhook管理
- [ ] `/system/social-login` - 社交登录配置

## 📊 开发效率评估

### ✅ AI开发优势
- **39个API服务**自动生成，直接调用
- **完整的权限系统**，开箱即用
- **标准化组件库**，UI一致性保障
- **完整的数据获取hooks**，支持分页、缓存、错误处理
- **企业级表格系统**：15个核心组件，支持复杂交互和高级过滤
- **类型安全**，TypeScript + API自动生成
- **中文化界面**，开箱即用的本地化支持

### 🚀 预期开发速度
- **单个页面开发时间**：20-40分钟（AI一次性完成，表格系统大幅提速）
- **40个页面总时间**：15-30小时（得益于新表格系统的效率提升）
- **预计完成时间**：1-3周（每天完成3-5个页面）

### 🎯 表格系统优势
- **零配置启动**：导入即用，无需从零构建表格
- **功能完整**：排序、过滤、分页、批量操作一应俱全
- **类型安全**：完整的TypeScript支持和智能提示
- **高度可定制**：支持复杂的业务逻辑和自定义组件
- **性能优化**：虚拟化渲染、智能缓存、懒加载
- **移动适配**：响应式设计，支持移动设备

基于现有基础设施和新的表格系统，所有页面可以更快速、高质量完成。