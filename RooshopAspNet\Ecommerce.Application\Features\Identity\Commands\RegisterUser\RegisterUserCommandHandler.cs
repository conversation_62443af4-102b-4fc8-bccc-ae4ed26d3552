using Ecommerce.Application.Extensions;
using Ecommerce.Application.Interfaces.External;
using Ecommerce.Domain.Aggregates.Identity;
using MediatR;
using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.WebUtilities;
using Microsoft.Extensions.Configuration;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading;
using System.Threading.Tasks;

namespace Ecommerce.Application.Features.Identity.Commands.RegisterUser;

/// <summary>
/// 用户注册命令处理程序
/// </summary>
public class RegisterUserCommandHandler : IRequestHandler<RegisterUserCommand, RegisterUserResponse>
{
    private readonly UserManager<ApplicationUser> _userManager;
    private readonly RoleManager<ApplicationRole> _roleManager;
    private readonly IEmailService _emailService;
    private readonly IEmailConfirmationSettings _emailConfirmationSettings;
    private readonly IVerificationCodeService _verificationCodeService;
    private readonly IConfiguration _configuration;

    /// <summary>
    /// 构造函数
    /// </summary>
    /// <param name="userManager">用户管理器</param>
    /// <param name="roleManager">角色管理器</param>
    /// <param name="emailService">邮件服务</param>
    /// <param name="emailConfirmationSettings">邮箱确认设置</param>
    /// <param name="verificationCodeService">验证码服务</param>
    /// <param name="configuration">配置</param>
    public RegisterUserCommandHandler(
        UserManager<ApplicationUser> userManager,
        RoleManager<ApplicationRole> roleManager,
        IEmailService emailService,
        IEmailConfirmationSettings emailConfirmationSettings,
        IVerificationCodeService verificationCodeService,
        IConfiguration configuration)
    {
        _userManager = userManager;
        _roleManager = roleManager;
        _emailService = emailService;
        _emailConfirmationSettings = emailConfirmationSettings;
        _verificationCodeService = verificationCodeService;
        _configuration = configuration;
    }

    /// <summary>
    /// 处理用户注册命令
    /// </summary>
    /// <param name="request">请求</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>响应</returns>
    public async Task<RegisterUserResponse> Handle(RegisterUserCommand request, CancellationToken cancellationToken)
    {
        var user = ApplicationUser.Create(
            request.UserName,
            request.Email,
            request.FirstName,
            request.LastName);

        // 设置手机号（如果提供）
        if (!string.IsNullOrEmpty(request.PhoneNumber))
        {
            user.PhoneNumber = request.PhoneNumber;
        }

        var result = await _userManager.CreateAsync(user, request.Password);
        if (!result.Succeeded)
        {
            return new RegisterUserResponse
            {
                IsSuccessful = false,
                Errors = result.Errors.Select(e => e.Description)
            };
        }

        // 默认分配Customer角色
        if (await _roleManager.RoleExistsAsync("Customer"))
        {
            await _userManager.AddToRoleAsync(user, "Customer");
        }

        // 更新用户的系统用户标识（注册用户默认为普通用户，is_system_user = false）
        await _userManager.UpdateSystemUserFlagAsync(user);

        // 生成邮箱确认令牌
        var token = await _userManager.GenerateEmailConfirmationTokenAsync(user);
        token = WebEncoders.Base64UrlEncode(Encoding.UTF8.GetBytes(token));

        // 构建确认链接
        // 添加过期时间
        var expiryTime = DateTime.UtcNow.AddHours(_emailConfirmationSettings.GetLinkExpirationHours()).ToString("o");
        var encodedExpiryTime = WebEncoders.Base64UrlEncode(Encoding.UTF8.GetBytes(expiryTime));

        // 从配置中获取API基础URL
        var apiBaseUrl = _configuration["AppSettings:ApiBaseUrl"];
        if (string.IsNullOrEmpty(apiBaseUrl))
        {
            apiBaseUrl = "http://localhost:5000"; // 默认值，以防配置中没有设置
        }

        var confirmationLink = $"{apiBaseUrl}/api/v1/accountsapi/confirm-email?userId={user.Id}&token={token}&expires={encodedExpiryTime}";

        // 发送确认邮件
        await SendEmailConfirmationAsync(user, confirmationLink, cancellationToken);

        // 不再自动登录，因为需要邮箱确认
        // await _signInManager.SignInAsync(user, isPersistent: false);

        return new RegisterUserResponse
        {
            Id = user.Id.ToString(),
            UserName = user.UserName!,
            Email = user.Email!,
            PhoneNumber = user.PhoneNumber ?? string.Empty,
            IsSuccessful = true,
            RequiresEmailConfirmation = true
        };
    }

    /// <summary>
    /// 发送邮箱确认邮件
    /// </summary>
    /// <param name="user">用户</param>
    /// <param name="confirmationLink">确认链接</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>发送结果</returns>
    private async Task<bool> SendEmailConfirmationAsync(
        ApplicationUser user,
        string confirmationLink,
        CancellationToken cancellationToken)
    {
        // 生成验证码
        var verificationCode = await _verificationCodeService.GenerateCodeAsync(
            user.Id.ToString(),
            "EmailConfirmation",
            _emailConfirmationSettings.GetLinkExpirationHours() * 60);

        var templateData = new Dictionary<string, object>
        {
            { "user_name", user.UserName ?? "" },
            { "company_name", "RooShop" },
            { "confirmation_link", confirmationLink },
            { "verification_code", verificationCode }
        };

        return await _emailService.SendTemplatedEmailAsync(
            user.Email!,
            "请确认您的电子邮箱",
            "EmailConfirmationTemplate",
            templateData,
            cancellationToken);
    }
}