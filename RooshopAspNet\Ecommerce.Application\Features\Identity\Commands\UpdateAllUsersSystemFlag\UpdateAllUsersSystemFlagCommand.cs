using MediatR;

namespace Ecommerce.Application.Features.Identity.Commands.UpdateAllUsersSystemFlag;

/// <summary>
/// 批量更新所有用户的系统用户标识命令
/// </summary>
public class UpdateAllUsersSystemFlagCommand : IRequest<UpdateAllUsersSystemFlagResponse>
{
    /// <summary>
    /// 是否强制更新（即使用户的 is_system_user 已经正确）
    /// </summary>
    public bool ForceUpdate { get; set; } = false;
}

/// <summary>
/// 批量更新所有用户的系统用户标识响应
/// </summary>
public class UpdateAllUsersSystemFlagResponse
{
    /// <summary>
    /// 是否成功
    /// </summary>
    public bool IsSuccessful { get; set; }

    /// <summary>
    /// 总用户数
    /// </summary>
    public int TotalUsers { get; set; }

    /// <summary>
    /// 成功更新的用户数
    /// </summary>
    public int UpdatedUsers { get; set; }

    /// <summary>
    /// 失败的用户数
    /// </summary>
    public int FailedUsers { get; set; }

    /// <summary>
    /// 错误信息
    /// </summary>
    public List<string> Errors { get; set; } = new();

    /// <summary>
    /// 详细结果
    /// </summary>
    public List<UserUpdateResult> Results { get; set; } = new();
}

/// <summary>
/// 用户更新结果
/// </summary>
public class UserUpdateResult
{
    /// <summary>
    /// 用户ID
    /// </summary>
    public string UserId { get; set; } = string.Empty;

    /// <summary>
    /// 用户名
    /// </summary>
    public string UserName { get; set; } = string.Empty;

    /// <summary>
    /// 邮箱
    /// </summary>
    public string Email { get; set; } = string.Empty;

    /// <summary>
    /// 更新前的系统用户标识
    /// </summary>
    public bool OldIsSystemUser { get; set; }

    /// <summary>
    /// 更新后的系统用户标识
    /// </summary>
    public bool NewIsSystemUser { get; set; }

    /// <summary>
    /// 是否成功
    /// </summary>
    public bool IsSuccessful { get; set; }

    /// <summary>
    /// 错误信息
    /// </summary>
    public string? ErrorMessage { get; set; }

    /// <summary>
    /// 用户角色
    /// </summary>
    public List<string> Roles { get; set; } = new();
}
