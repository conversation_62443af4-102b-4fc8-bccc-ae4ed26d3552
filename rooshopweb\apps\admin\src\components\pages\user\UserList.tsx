'use client';

import { useState, useMemo, useCallback } from 'react';
import { usePagedQuery } from '@rooshop/core/data';
import { useAdminTranslations } from '@rooshop/core/i18n';
import { PermissionGuard, withPermission } from '@rooshop/core';
import { formatErrorMessage } from '@rooshop/core/api';
import { UsersApiService, AccountsApiService } from '@rooshop/core/api/generated';
import type { GetUserResponse, CreateUserCommand, UpdateUserCommand, UpdateUserStatusCommand, SuspendUserCommand, DeactivateAccountCommand } from '@rooshop/core/api/generated';
import {
  DataTable,
  DataTableToolbar,
  DataTableColumnHeader,
  type Option
} from '@app/components/tables';
import { Button } from '@app/components/ui/button';
import { Badge } from '@app/components/ui/badge';
import { Checkbox } from '@app/components/ui/checkbox';
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger, DropdownMenuSeparator } from '@app/components/ui/dropdown-menu';
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle } from '@app/components/ui/dialog';
import { Input } from '@app/components/ui/input';
import { Label } from '@app/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@app/components/ui/select';
import { Textarea } from '@app/components/ui/textarea';
import { Switch } from '@app/components/ui/switch';
import { Plus, MoreVertical, Edit, Trash, User, Shield, UserX, UserCheck, Clock, AlertTriangle } from 'lucide-react';
import { toast } from 'sonner';
import { useReactTable, getCoreRowModel, getSortedRowModel, getFilteredRowModel, getPaginationRowModel, type ColumnDef } from '@tanstack/react-table';

// 用户数据类型定义（基于后端GetUserResponse）
interface User extends GetUserResponse {
  id: string;
  userName: string;
  email: string;
  firstName?: string;
  lastName?: string;
  phoneNumber?: string;
  avatarUrl?: string;
  isActive: boolean;
  isEmailConfirmed?: boolean;
  isPhoneConfirmed?: boolean;
  isSystemUser?: boolean;
  status?: string;
  roles?: string[];
  createdAt: string;
  lastLoginAt?: string;
  deletedAt?: string;
}

// 用户状态选项（基于后端状态枚举）
const statusOptions: Option[] = [
  { label: '活跃', value: 'Active' },
  { label: '非活跃', value: 'Inactive' },
  { label: '待审核', value: 'Pending' },
  { label: '已暂停', value: 'Suspended' },
  { label: '已注销', value: 'Deactivated' },
  { label: '已删除', value: 'Deleted' },
];

// 用户类型选项
const userTypeOptions: Option[] = [
  { label: '全部用户', value: 'all' },
  { label: '普通用户', value: 'regular' },
  { label: '系统用户', value: 'system' },
];

function UserList() {
  const [createDialogOpen, setCreateDialogOpen] = useState(false);
  const [editDialogOpen, setEditDialogOpen] = useState(false);
  const [suspendDialogOpen, setSuspendDialogOpen] = useState(false);
  const [deactivateDialogOpen, setDeactivateDialogOpen] = useState(false);
  const [selectedUser, setSelectedUser] = useState<User | null>(null);
  const [isUpdating, setIsUpdating] = useState(false);
  const [userType, setUserType] = useState<'all' | 'regular' | 'system'>('all');

  // 表单数据状态
  const [formData, setFormData] = useState<{
    username: string;
    email: string;
    password: string;
    confirmPassword: string;
    phoneNumber: string;
    firstName: string;
    lastName: string;
    status: string;
    roles: string[];
    requireEmailConfirmation: boolean;
  }>({
    username: '',
    email: '',
    password: '',
    confirmPassword: '',
    phoneNumber: '',
    firstName: '',
    lastName: '',
    status: 'Active',
    roles: [],
    requireEmailConfirmation: true
  });

  // 暂停用户表单数据
  const [suspendData, setSuspendData] = useState<{
    reason: string;
    suspendUntil: string;
  }>({
    reason: '',
    suspendUntil: ''
  });

  // 注销账户表单数据
  const [deactivateData, setDeactivateData] = useState<{
    reason: string;
    password: string;
    immediateDeactivation: boolean;
  }>({
    reason: '',
    password: '',
    immediateDeactivation: false
  });

  const t = useAdminTranslations('user');

  // 数据获取 - 使用统一的API端点，在客户端进行筛选
  const { data, error, isLoading, mutate } = usePagedQuery<User>('/UsersApi', {
    page: 1,
    pageSize: 10
  });

  // 根据用户类型筛选数据
  const filteredData = useMemo(() => {
    if (!data?.items) return data;

    let filteredItems = data.items;

    if (userType === 'system') {
      filteredItems = data.items.filter(user => user.isSystemUser === true);
    } else if (userType === 'regular') {
      filteredItems = data.items.filter(user => user.isSystemUser !== true);
    }
    // userType === 'all' 时不进行筛选

    return {
      ...data,
      items: filteredItems,
      totalCount: filteredItems.length
    };
  }, [data, userType]);

  // 事件处理函数
  const handleEdit = useCallback((user: User) => {
    setSelectedUser(user);
    setFormData({
      username: user.userName || '',
      email: user.email || '',
      password: '',
      confirmPassword: '',
      phoneNumber: user.phoneNumber || '',
      firstName: user.firstName || '',
      lastName: user.lastName || '',
      status: user.status || (user.isActive ? 'Active' : 'Inactive'),
      roles: user.roles || [],
      requireEmailConfirmation: true
    });
    setEditDialogOpen(true);
  }, []);

  const handleDelete = useCallback(async (id: string) => {
    try {
      // 使用生成的API服务进行删除操作
      await UsersApiService.deleteApiV1UsersApi(id);
      toast.success(t('deleteSuccess'));
      mutate();
    } catch (error) {
      toast.error(formatErrorMessage(error));
    }
  }, [t, mutate]);

  // 暂停用户
  const handleSuspend = useCallback((user: User) => {
    setSelectedUser(user);
    setSuspendData({
      reason: '',
      suspendUntil: ''
    });
    setSuspendDialogOpen(true);
  }, []);

  // 执行暂停用户操作
  const handleSuspendUser = useCallback(async () => {
    if (!selectedUser) return;

    try {
      setIsUpdating(true);
      const suspendCommand: SuspendUserCommand = {
        userId: selectedUser.id,
        reason: suspendData.reason,
        suspendUntil: suspendData.suspendUntil || undefined
      };

      await UsersApiService.patchApiV1UsersApiSuspend(selectedUser.id, suspendCommand);
      toast.success('用户已暂停');
      setSuspendDialogOpen(false);
      setSelectedUser(null);
      setSuspendData({ reason: '', suspendUntil: '' });
      mutate();
    } catch (error) {
      toast.error(formatErrorMessage(error));
    } finally {
      setIsUpdating(false);
    }
  }, [selectedUser, suspendData, mutate]);

  // 注销账户
  const handleDeactivate = useCallback((user: User) => {
    setSelectedUser(user);
    setDeactivateData({
      reason: '',
      password: '',
      immediateDeactivation: false
    });
    setDeactivateDialogOpen(true);
  }, []);

  // 执行注销账户操作
  const handleDeactivateAccount = useCallback(async () => {
    if (!selectedUser) return;

    try {
      setIsUpdating(true);
      const deactivateCommand: DeactivateAccountCommand = {
        userId: selectedUser.id,
        reason: deactivateData.reason,
        password: deactivateData.password,
        immediateDeactivation: deactivateData.immediateDeactivation
      };

      await AccountsApiService.postApiV1AccountsApiDeactivate(deactivateCommand);
      toast.success('账户已注销');
      setDeactivateDialogOpen(false);
      setSelectedUser(null);
      setDeactivateData({ reason: '', password: '', immediateDeactivation: false });
      mutate();
    } catch (error) {
      toast.error(formatErrorMessage(error));
    } finally {
      setIsUpdating(false);
    }
  }, [selectedUser, deactivateData, mutate]);

  // 更新用户状态
  const handleUpdateStatus = useCallback(async (userId: string, status: string) => {
    try {
      const updateStatusCommand: UpdateUserStatusCommand = {
        userId,
        status
      };

      await UsersApiService.patchApiV1UsersApiStatus(userId, updateStatusCommand);
      toast.success('用户状态已更新');
      mutate();
    } catch (error) {
      toast.error(formatErrorMessage(error));
    }
  }, [mutate]);

  // 表格列定义
  const columns = useMemo<ColumnDef<User>[]>(() => [
    {
      id: 'select',
      header: ({ table }) => (
        <Checkbox
          checked={table.getIsAllPageRowsSelected()}
          onCheckedChange={(value) => table.toggleAllPageRowsSelected(!!value)}
          aria-label="选择全部"
        />
      ),
      cell: ({ row }) => (
        <Checkbox
          checked={row.getIsSelected()}
          onCheckedChange={(value) => row.toggleSelected(!!value)}
          aria-label="选择行"
        />
      ),
      enableSorting: false,
      enableHiding: false,
    },
    {
      accessorKey: 'userName',
      header: ({ column }) => (
        <DataTableColumnHeader column={column} title={t('username')} />
      ),
      cell: ({ row }) => (
        <div className="flex items-center gap-2">
          <User className="h-4 w-4 text-muted-foreground" />
          <span className="font-medium">{row.original.userName}</span>
        </div>
      ),
      meta: {
        label: t('username'),
        variant: 'text',
        placeholder: '搜索用户名...',
      },
      enableColumnFilter: true,
    },
    {
      accessorKey: 'email',
      header: ({ column }) => (
        <DataTableColumnHeader column={column} title={t('email')} />
      ),
      meta: {
        label: t('email'),
        variant: 'text',
        placeholder: '搜索邮箱...',
      },
      enableColumnFilter: true,
    },
    {
      accessorKey: 'firstName',
      header: ({ column }) => (
        <DataTableColumnHeader column={column} title={t('firstName')} />
      ),
      cell: ({ row }) => row.original.firstName || '-',
    },
    {
      accessorKey: 'lastName',
      header: ({ column }) => (
        <DataTableColumnHeader column={column} title={t('lastName')} />
      ),
      cell: ({ row }) => row.original.lastName || '-',
    },
    {
      accessorKey: 'status',
      header: ({ column }) => (
        <DataTableColumnHeader column={column} title={t('statusLabel')} />
      ),
      cell: ({ row }) => {
        const user = row.original;
        const status = user.status || (user.isActive ? 'Active' : 'Inactive');

        // 根据状态显示不同的徽章样式
        const getBadgeVariant = (status: string) => {
          switch (status) {
            case 'Active': return 'default';
            case 'Inactive': return 'secondary';
            case 'Pending': return 'outline';
            case 'Suspended': return 'destructive';
            case 'Deactivated': return 'secondary';
            case 'Deleted': return 'destructive';
            default: return 'outline';
          }
        };

        const getStatusIcon = (status: string) => {
          switch (status) {
            case 'Active': return <UserCheck className="h-3 w-3 mr-1" />;
            case 'Suspended': return <UserX className="h-3 w-3 mr-1" />;
            case 'Pending': return <Clock className="h-3 w-3 mr-1" />;
            case 'Deactivated': return <AlertTriangle className="h-3 w-3 mr-1" />;
            default: return null;
          }
        };

        return (
          <Badge variant={getBadgeVariant(status)} className="flex items-center w-fit">
            {getStatusIcon(status)}
            {statusOptions.find(opt => opt.value === status)?.label || status}
          </Badge>
        );
      },
      meta: {
        label: '状态',
        variant: 'multiSelect',
        options: statusOptions,
      },
      enableColumnFilter: true,
    },
    {
      accessorKey: 'isSystemUser',
      header: ({ column }) => (
        <DataTableColumnHeader column={column} title="用户类型" />
      ),
      cell: ({ row }) => {
        const isSystemUser = row.original.isSystemUser;
        return isSystemUser ? (
          <Badge variant="outline" className="text-xs">
            <Shield className="h-3 w-3 mr-1" />
            系统用户
          </Badge>
        ) : (
          <span className="text-muted-foreground text-xs">普通用户</span>
        );
      },
      enableSorting: false,
    },
    {
      accessorKey: 'roles',
      header: ({ column }) => (
        <DataTableColumnHeader column={column} title={t('roles')} />
      ),
      cell: ({ row }) => {
        const roles = row.original.roles || [];
        return roles.length > 0 ? (
          <div className="flex gap-1 flex-wrap">
            {roles.slice(0, 2).map((role, index) => (
              <Badge key={index} variant="outline" className="text-xs">
                <Shield className="h-3 w-3 mr-1" />
                {role}
              </Badge>
            ))}
            {roles.length > 2 && (
              <Badge variant="outline" className="text-xs">
                +{roles.length - 2}
              </Badge>
            )}
          </div>
        ) : (
          <span className="text-muted-foreground">-</span>
        );
      },
      enableSorting: false,
    },
    {
      id: 'actions',
      header: t('actions'),
      cell: ({ row }) => {
        const user = row.original;
        const status = user.status || (user.isActive ? 'Active' : 'Inactive');

        return (
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="ghost" className="h-8 w-8 p-0">
                <MoreVertical className="h-4 w-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <PermissionGuard requiredPermission="Permissions.Users.EditUsers">
                <DropdownMenuItem onClick={() => handleEdit(user)}>
                  <Edit className="h-4 w-4 mr-2" />
                  {t('edit')}
                </DropdownMenuItem>
              </PermissionGuard>

              {/* 状态管理操作 */}
              <PermissionGuard requiredPermission="Permissions.Users.EditUsers">
                <DropdownMenuSeparator />
                {status === 'Active' && (
                  <DropdownMenuItem onClick={() => handleUpdateStatus(user.id, 'Inactive')}>
                    <UserX className="h-4 w-4 mr-2" />
                    停用用户
                  </DropdownMenuItem>
                )}
                {status === 'Inactive' && (
                  <DropdownMenuItem onClick={() => handleUpdateStatus(user.id, 'Active')}>
                    <UserCheck className="h-4 w-4 mr-2" />
                    激活用户
                  </DropdownMenuItem>
                )}
                {status !== 'Suspended' && status !== 'Deleted' && (
                  <DropdownMenuItem onClick={() => handleSuspend(user)}>
                    <Clock className="h-4 w-4 mr-2" />
                    暂停用户
                  </DropdownMenuItem>
                )}
              </PermissionGuard>

              {/* 账户注销操作 */}
              <PermissionGuard requiredPermission="Permissions.Users.EditUsers">
                {status !== 'Deactivated' && status !== 'Deleted' && (
                  <DropdownMenuItem onClick={() => handleDeactivate(user)}>
                    <AlertTriangle className="h-4 w-4 mr-2" />
                    注销账户
                  </DropdownMenuItem>
                )}
              </PermissionGuard>

              {/* 删除操作 */}
              <PermissionGuard requiredPermission="Permissions.Users.Delete">
                <DropdownMenuSeparator />
                <DropdownMenuItem
                  onClick={() => handleDelete(user.id)}
                  className="text-destructive"
                >
                  <Trash className="h-4 w-4 mr-2" />
                  {t('delete')}
                </DropdownMenuItem>
              </PermissionGuard>
            </DropdownMenuContent>
          </DropdownMenu>
        );
      },
      enableSorting: false,
      enableHiding: false,
    }
  ], [t, handleEdit, handleDelete, handleSuspend, handleDeactivate, handleUpdateStatus]);

  // 初始化表格
  const table = useReactTable({
    data: filteredData?.items || [],
    columns,
    getCoreRowModel: getCoreRowModel(),
    getSortedRowModel: getSortedRowModel(),
    getFilteredRowModel: getFilteredRowModel(),
    getPaginationRowModel: getPaginationRowModel(),
    enableRowSelection: true,
    getRowId: (row) => row.id,
  });

  // 其他事件处理函数
  const handleCreate = useCallback(async () => {
    try {
      // 将前端数据格式转换为后端期望的格式
      const createData: CreateUserCommand = {
        userName: formData.username,
        email: formData.email,
        password: formData.password,
        confirmPassword: formData.confirmPassword,
        phoneNumber: formData.phoneNumber || undefined,
        firstName: formData.firstName || undefined,
        lastName: formData.lastName || undefined,
        status: formData.status,
        roles: formData.roles,
        requireEmailConfirmation: formData.requireEmailConfirmation
      };

      await UsersApiService.postApiV1UsersApi(createData);
      toast.success(t('createSuccess'));
      setCreateDialogOpen(false);
      setFormData({
        username: '',
        email: '',
        password: '',
        confirmPassword: '',
        phoneNumber: '',
        firstName: '',
        lastName: '',
        status: 'Active',
        roles: [],
        requireEmailConfirmation: true
      });
      mutate();
    } catch (error) {
      toast.error(formatErrorMessage(error));
    }
  }, [formData, t, mutate]);

  const handleUpdate = useCallback(async () => {
    if (!selectedUser) return;

    try {
      setIsUpdating(true);
      // 将前端数据格式转换为后端期望的格式
      const updateData: UpdateUserCommand = {
        userId: selectedUser.id,
        firstName: formData.firstName || undefined,
        lastName: formData.lastName || undefined,
        phoneNumber: formData.phoneNumber || undefined,
        avatarUrl: undefined // 暂时不支持头像更新
      };

      await UsersApiService.putApiV1UsersApi(selectedUser.id, updateData);
      toast.success(t('updateSuccess'));
      setEditDialogOpen(false);
      setSelectedUser(null);
      mutate();
    } catch (error) {
      toast.error(formatErrorMessage(error));
    } finally {
      setIsUpdating(false);
    }
  }, [selectedUser, formData, t, mutate]);

  if (error) {
    return (
      <div className="flex h-64 w-full items-center justify-center">
        <div className="text-center">
          <p className="text-destructive">{formatErrorMessage(error)}</p>
          <Button onClick={() => mutate()} className="mt-2">
            重试
          </Button>
        </div>
      </div>
    );
  }

  if (isLoading) {
    return (
      <div className="flex h-64 w-full items-center justify-center">
        <div className="text-center">
          <p>加载中...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* 数据表格 */}
      <DataTable table={table}>
        <DataTableToolbar table={table}>
          <div className="flex items-center gap-2">
            {/* 用户类型筛选 */}
            <Select value={userType} onValueChange={(value: 'all' | 'regular' | 'system') => setUserType(value)}>
              <SelectTrigger className="w-[140px] h-8">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                {userTypeOptions.map((option) => (
                  <SelectItem key={option.value} value={option.value}>
                    {option.label}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>

            <PermissionGuard requiredPermission="Permissions.Users.Create">
              <Button
                variant="outline"
                size="sm"
                className="h-8"
                onClick={() => setCreateDialogOpen(true)}
              >
                <Plus className="mr-2 size-4" />
                {t('create')}
              </Button>
            </PermissionGuard>
          </div>
        </DataTableToolbar>
      </DataTable>

      {/* 创建用户对话框 */}
      <Dialog open={createDialogOpen} onOpenChange={setCreateDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>{t('create')}</DialogTitle>
            <DialogDescription>
              {t('createDescription')}
            </DialogDescription>
          </DialogHeader>

          <div className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="username">{t('username')}</Label>
              <Input
                id="username"
                value={formData.username}
                onChange={(e) => setFormData(prev => ({ ...prev, username: e.target.value }))}
                placeholder={t('usernamePlaceholder')}
                required
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="email">{t('email')}</Label>
              <Input
                id="email"
                type="email"
                value={formData.email}
                onChange={(e) => setFormData(prev => ({ ...prev, email: e.target.value }))}
                placeholder={t('emailPlaceholder')}
                required
              />
            </div>

            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="password">密码</Label>
                <Input
                  id="password"
                  type="password"
                  value={formData.password}
                  onChange={(e) => setFormData(prev => ({ ...prev, password: e.target.value }))}
                  placeholder="请输入密码"
                  required
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="confirmPassword">确认密码</Label>
                <Input
                  id="confirmPassword"
                  type="password"
                  value={formData.confirmPassword}
                  onChange={(e) => setFormData(prev => ({ ...prev, confirmPassword: e.target.value }))}
                  placeholder="请再次输入密码"
                  required
                />
              </div>
            </div>

            <div className="space-y-2">
              <Label htmlFor="phoneNumber">手机号码</Label>
              <Input
                id="phoneNumber"
                type="tel"
                value={formData.phoneNumber}
                onChange={(e) => setFormData(prev => ({ ...prev, phoneNumber: e.target.value }))}
                placeholder="请输入手机号码"
              />
            </div>

            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="firstName">{t('firstName')}</Label>
                <Input
                  id="firstName"
                  value={formData.firstName}
                  onChange={(e) => setFormData(prev => ({ ...prev, firstName: e.target.value }))}
                  placeholder={t('firstNamePlaceholder')}
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="lastName">{t('lastName')}</Label>
                <Input
                  id="lastName"
                  value={formData.lastName}
                  onChange={(e) => setFormData(prev => ({ ...prev, lastName: e.target.value }))}
                  placeholder={t('lastNamePlaceholder')}
                />
              </div>
            </div>

            <div className="space-y-2">
              <Label htmlFor="status">{t('statusLabel')}</Label>
              <Select value={formData.status} onValueChange={(value: string) =>
                setFormData(prev => ({ ...prev, status: value }))
              }>
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  {statusOptions.slice(0, 3).map((option) => ( // 只显示前3个状态用于创建
                    <SelectItem key={option.value} value={option.value}>
                      {option.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div className="flex items-center space-x-2">
              <Switch
                id="requireEmailConfirmation"
                checked={formData.requireEmailConfirmation}
                onCheckedChange={(checked) => setFormData(prev => ({ ...prev, requireEmailConfirmation: checked }))}
              />
              <Label htmlFor="requireEmailConfirmation">需要邮箱验证</Label>
            </div>
          </div>

          <div className="flex justify-end gap-2 pt-4">
            <Button variant="outline" onClick={() => setCreateDialogOpen(false)}>
              {t('cancel')}
            </Button>
            <Button onClick={handleCreate} disabled={!formData.username || !formData.email || !formData.password}>
              创建用户
            </Button>
          </div>
        </DialogContent>
      </Dialog>

      {/* 编辑用户对话框 */}
      <Dialog open={editDialogOpen} onOpenChange={setEditDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>{t('edit')}</DialogTitle>
            <DialogDescription>
              {t('editDescription')}
            </DialogDescription>
          </DialogHeader>

          <div className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="edit-username">{t('username')}</Label>
              <Input
                id="edit-username"
                value={formData.username}
                onChange={(e) => setFormData(prev => ({ ...prev, username: e.target.value }))}
                placeholder={t('usernamePlaceholder')}
                disabled // 用户名通常不允许修改
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="edit-email">{t('email')}</Label>
              <Input
                id="edit-email"
                type="email"
                value={formData.email}
                onChange={(e) => setFormData(prev => ({ ...prev, email: e.target.value }))}
                placeholder={t('emailPlaceholder')}
                disabled // 邮箱通常不允许修改
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="edit-phoneNumber">手机号码</Label>
              <Input
                id="edit-phoneNumber"
                type="tel"
                value={formData.phoneNumber}
                onChange={(e) => setFormData(prev => ({ ...prev, phoneNumber: e.target.value }))}
                placeholder="请输入手机号码"
              />
            </div>

            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="edit-firstName">{t('firstName')}</Label>
                <Input
                  id="edit-firstName"
                  value={formData.firstName}
                  onChange={(e) => setFormData(prev => ({ ...prev, firstName: e.target.value }))}
                  placeholder={t('firstNamePlaceholder')}
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="edit-lastName">{t('lastName')}</Label>
                <Input
                  id="edit-lastName"
                  value={formData.lastName}
                  onChange={(e) => setFormData(prev => ({ ...prev, lastName: e.target.value }))}
                  placeholder={t('lastNamePlaceholder')}
                />
              </div>
            </div>
          </div>

          <div className="flex justify-end gap-2 pt-4">
            <Button variant="outline" onClick={() => setEditDialogOpen(false)}>
              {t('cancel')}
            </Button>
            <Button onClick={handleUpdate} disabled={isUpdating}>
              {isUpdating ? t('updating') : t('update')}
            </Button>
          </div>
        </DialogContent>
      </Dialog>

      {/* 暂停用户对话框 */}
      <Dialog open={suspendDialogOpen} onOpenChange={setSuspendDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>暂停用户</DialogTitle>
            <DialogDescription>
              暂停用户将禁止其登录和使用系统功能。您可以设置暂停期限或无限期暂停。
            </DialogDescription>
          </DialogHeader>

          <div className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="suspend-reason">暂停原因</Label>
              <Textarea
                id="suspend-reason"
                value={suspendData.reason}
                onChange={(e) => setSuspendData(prev => ({ ...prev, reason: e.target.value }))}
                placeholder="请输入暂停原因..."
                rows={3}
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="suspend-until">暂停到期时间（可选）</Label>
              <Input
                id="suspend-until"
                type="datetime-local"
                value={suspendData.suspendUntil}
                onChange={(e) => setSuspendData(prev => ({ ...prev, suspendUntil: e.target.value }))}
              />
              <p className="text-sm text-muted-foreground">
                如果不设置到期时间，将无限期暂停
              </p>
            </div>
          </div>

          <div className="flex justify-end gap-2 pt-4">
            <Button variant="outline" onClick={() => setSuspendDialogOpen(false)}>
              取消
            </Button>
            <Button
              variant="destructive"
              onClick={handleSuspendUser}
              disabled={isUpdating || !suspendData.reason.trim()}
            >
              {isUpdating ? '暂停中...' : '确认暂停'}
            </Button>
          </div>
        </DialogContent>
      </Dialog>

      {/* 注销账户对话框 */}
      <Dialog open={deactivateDialogOpen} onOpenChange={setDeactivateDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>注销账户</DialogTitle>
            <DialogDescription>
              注销账户是一个严重的操作，将禁用用户的所有功能。请谨慎操作。
            </DialogDescription>
          </DialogHeader>

          <div className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="deactivate-reason">注销原因</Label>
              <Textarea
                id="deactivate-reason"
                value={deactivateData.reason}
                onChange={(e) => setDeactivateData(prev => ({ ...prev, reason: e.target.value }))}
                placeholder="请输入注销原因..."
                rows={3}
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="deactivate-password">管理员密码确认</Label>
              <Input
                id="deactivate-password"
                type="password"
                value={deactivateData.password}
                onChange={(e) => setDeactivateData(prev => ({ ...prev, password: e.target.value }))}
                placeholder="请输入您的密码以确认操作"
              />
            </div>

            <div className="flex items-center space-x-2">
              <Switch
                id="immediate-deactivation"
                checked={deactivateData.immediateDeactivation}
                onCheckedChange={(checked) => setDeactivateData(prev => ({ ...prev, immediateDeactivation: checked }))}
              />
              <Label htmlFor="immediate-deactivation">立即注销（否则给用户7天反悔期）</Label>
            </div>
          </div>

          <div className="flex justify-end gap-2 pt-4">
            <Button variant="outline" onClick={() => setDeactivateDialogOpen(false)}>
              取消
            </Button>
            <Button
              variant="destructive"
              onClick={handleDeactivateAccount}
              disabled={isUpdating || !deactivateData.reason.trim() || !deactivateData.password.trim()}
            >
              {isUpdating ? '注销中...' : '确认注销'}
            </Button>
          </div>
        </DialogContent>
      </Dialog>
    </div>
  );
}

export default withPermission(UserList, "Permissions.Users.View");