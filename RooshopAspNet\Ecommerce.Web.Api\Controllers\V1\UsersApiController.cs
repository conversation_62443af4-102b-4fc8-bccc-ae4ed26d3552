using Ecommerce.Application.Common.Models;
using Ecommerce.Application.Features.Identity.Commands.CreateUser;
using Ecommerce.Application.Features.Identity.Commands.DeleteUser;
using Ecommerce.Application.Features.Identity.Commands.UpdateAllUsersSystemFlag;
using Ecommerce.Application.Features.Identity.Commands.SuspendUser;
using Ecommerce.Application.Features.Identity.Commands.UpdateUser;
using Ecommerce.Application.Features.Identity.Commands.UpdateUserStatus;
using Ecommerce.Application.Features.Identity.Queries.GetUser;
using Ecommerce.Application.Features.Identity.Queries.GetUsers;
using Ecommerce.Application.Features.Identity.Queries.GetSystemUsers;
using Ecommerce.Domain.Aggregates.Identity;
using Ecommerce.Domain.Constants;
using Ecommerce.Infrastructure.Identity.Authorization;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using System;
using System.IdentityModel.Tokens.Jwt;
using System.Linq;
using System.Security.Claims;
using System.Threading.Tasks;

namespace Ecommerce.Web.Api.Controllers.V1
{
    [ApiController]
    [Route("api/v{version:apiVersion}/[controller]")]
    [ApiVersion("1.0")]
    [Authorize]
    public class UsersApiController : BaseApiController
    {
        private readonly ILogger<UsersApiController> _logger;
        private static readonly string[] UserNotLoggedInError = ["用户未登录"];
        private static readonly string[] UserNotFoundError = ["用户不存在"];

        public UsersApiController(ILogger<UsersApiController> logger)
        {
            _logger = logger;
        }

        /// <summary>
        /// 获取当前用户信息
        /// </summary>
        [HttpGet("me")]
        [RequiresPermission(PermissionConstants.ViewUsers)]
        [ProducesResponseType(typeof(ApiResponse<GetUserResponse>), 200)]
        [ProducesResponseType(typeof(ApiResponse), 401)]
        [ProducesResponseType(typeof(ApiResponse), 404)]
        public async Task<ActionResult<ApiResponse<GetUserResponse>>> GetCurrentUser()
        {
            // 获取所有声明，用于调试
            var claims = User.Claims.Select(c => new { c.Type, c.Value }).ToList();
            _logger.LogInformation("用户声明: {Claims}", System.Text.Json.JsonSerializer.Serialize(claims));

            // 获取用户ID
            var userId = User.FindFirst(ClaimTypes.NameIdentifier)?.Value;

            _logger.LogInformation("用户ID声明 - NameIdentifier: {NameId}", userId);
            if (string.IsNullOrEmpty(userId))
            {
                _logger.LogWarning("未找到用户ID声明");
                return ApiFail<GetUserResponse>("未授权", UserNotLoggedInError, 401);
            }

            _logger.LogInformation("将使用用户ID: {UserId} 查询用户信息", userId);

            // 尝试直接使用UserManager查询用户
            var userManager = HttpContext.RequestServices.GetRequiredService<UserManager<ApplicationUser>>();
            var user = await userManager.FindByIdAsync(userId);

            if (user != null)
            {
                _logger.LogInformation("使用UserManager直接找到用户: {UserId}, 用户名: {UserName}",
                    user.Id, user.UserName);
            }
            else
            {
                _logger.LogWarning("使用UserManager无法直接找到用户: {UserId}", userId);
            }

            // 使用MediatR查询用户
            var query = new GetUserQuery { UserId = userId };
            var result = await Mediator.Send(query);

            if (result == null)
            {
                _logger.LogWarning("使用MediatR无法找到用户: {UserId}", userId);
                return ApiFail<GetUserResponse>("未找到用户", UserNotFoundError, 404);
            }

            _logger.LogInformation("成功获取用户信息: {UserId}, 用户名: {UserName}", result.Id, result.UserName);
            return ApiSuccess(result);
        }

        /// <summary>
        /// 获取指定用户信息
        /// </summary>
        [HttpGet("{id}")]
        [RequiresPermission(PermissionConstants.ViewUsers)]
        [ProducesResponseType(typeof(ApiResponse<GetUserResponse>), 200)]
        [ProducesResponseType(typeof(ApiResponse), 401)]
        [ProducesResponseType(typeof(ApiResponse), 404)]
        public async Task<ActionResult<ApiResponse<GetUserResponse>>> GetUser(string id)
        {
            var query = new GetUserQuery { UserId = id };
            var result = await Mediator.Send(query);

            if (result == null)
            {
                return ApiFail<GetUserResponse>("未找到用户", UserNotFoundError, 404);
            }

            return ApiSuccess(result);
        }

        /// <summary>
        /// 更新当前用户信息
        /// </summary>
        [HttpPut("me")]
        [RequiresPermission(PermissionConstants.EditUsers)]
        [ProducesResponseType(typeof(ApiResponse<UpdateUserResponse>), 200)]
        [ProducesResponseType(typeof(ApiResponse), 400)]
        [ProducesResponseType(typeof(ApiResponse), 401)]
        public async Task<ActionResult<ApiResponse<UpdateUserResponse>>> UpdateCurrentUser([FromBody] UpdateUserCommand command)
        {
            var userId = User.FindFirst(ClaimTypes.NameIdentifier)?.Value;
            if (string.IsNullOrEmpty(userId))
            {
                return ApiFail<UpdateUserResponse>("未授权", UserNotLoggedInError, 401);
            }

            command.UserId = userId;
            var result = await Mediator.Send(command);

            if (!result.IsSuccessful)
            {
                return ApiFail<UpdateUserResponse>("更新失败", result.Errors?.ToArray() ?? [], 400);
            }

            return ApiSuccess(result);
        }

        /// <summary>
        /// 更新指定用户信息
        /// </summary>
        [HttpPut("{id}")]
        [RequiresPermission(PermissionConstants.EditUsers)]
        [ProducesResponseType(typeof(ApiResponse<UpdateUserResponse>), 200)]
        [ProducesResponseType(typeof(ApiResponse), 400)]
        public async Task<ActionResult<ApiResponse<UpdateUserResponse>>> UpdateUser(string id, [FromBody] UpdateUserCommand command)
        {
            command.UserId = id;
            var result = await Mediator.Send(command);

            if (!result.IsSuccessful)
            {
                return ApiFail<UpdateUserResponse>("更新失败", result.Errors?.ToArray() ?? [], 400);
            }

            return ApiSuccess(result);
        }

        /// <summary>
        /// 获取用户列表
        /// </summary>
        [HttpGet]
        [RequiresPermission(PermissionConstants.ViewUsers)]
        [ProducesResponseType(typeof(ApiResponse<PaginatedList<GetUserResponse>>), 200)]
        [ProducesResponseType(typeof(ApiResponse), 401)]
        public async Task<ActionResult<ApiResponse<PaginatedList<GetUserResponse>>>> GetUsers(
            [FromQuery] string? keyword = null,
            [FromQuery] string? status = null,
            [FromQuery] DateTime? startDate = null,
            [FromQuery] DateTime? endDate = null,
            [FromQuery] string? sortField = null,
            [FromQuery] string? sortOrder = null,
            [FromQuery] int page = 1,
            [FromQuery] int pageSize = 10)
        {
            var query = new GetUsersQuery
            {
                Keyword = keyword,
                Status = status,
                StartDate = startDate,
                EndDate = endDate,
                SortField = sortField,
                SortOrder = sortOrder,
                Page = page,
                PageSize = pageSize
            };

            var result = await Mediator.Send(query);
            return ApiSuccess(result);
        }

        /// <summary>
        /// 创建用户
        /// </summary>
        [HttpPost]
        [RequiresPermission(PermissionConstants.CreateUsers)]
        [ProducesResponseType(typeof(ApiResponse<CreateUserResponse>), 200)]
        [ProducesResponseType(typeof(ApiResponse), 400)]
        public async Task<ActionResult<ApiResponse<CreateUserResponse>>> CreateUser([FromBody] CreateUserCommand command)
        {
            var result = await Mediator.Send(command);

            if (!result.IsSuccessful)
            {
                return ApiFail<CreateUserResponse>("创建用户失败", result.Errors?.ToArray() ?? [], 400);
            }

            return ApiSuccess(result, "创建用户成功");
        }

        /// <summary>
        /// 删除用户
        /// </summary>
        [HttpDelete("{id}")]
        [RequiresPermission(PermissionConstants.DeleteUsers)]
        [ProducesResponseType(typeof(ApiResponse), 200)]
        [ProducesResponseType(typeof(ApiResponse), 400)]
        [ProducesResponseType(typeof(ApiResponse), 404)]
        public async Task<ActionResult<ApiResponse>> DeleteUser(string id)
        {
            var command = new DeleteUserCommand { UserId = id };
            var result = await Mediator.Send(command);

            if (!result.IsSuccessful)
            {
                if (result.ErrorCode == "USER_NOT_FOUND")
                {
                    return ApiFail("删除用户失败", result.Errors?.ToArray() ?? UserNotFoundError, 404);
                }

                return ApiFail("删除用户失败", result.Errors?.ToArray() ?? [], 400);
            }

            return ApiSuccess(message: "删除用户成功");
        }

        /// <summary>
        /// 更新用户状态
        /// </summary>
        [HttpPatch("{id}/status")]
        [RequiresPermission(PermissionConstants.EditUsers)]
        [ProducesResponseType(typeof(ApiResponse<UpdateUserStatusResponse>), 200)]
        [ProducesResponseType(typeof(ApiResponse), 400)]
        [ProducesResponseType(typeof(ApiResponse), 404)]
        public async Task<ActionResult<ApiResponse<UpdateUserStatusResponse>>> UpdateUserStatus(
            string id,
            [FromBody] UpdateUserStatusCommand command)
        {
            command.UserId = id;
            var result = await Mediator.Send(command);

            if (!result.IsSuccessful)
            {
                if (result.ErrorCode == "USER_NOT_FOUND")
                {
                    return ApiFail<UpdateUserStatusResponse>("更新用户状态失败", result.Errors?.ToArray() ?? UserNotFoundError, 404);
                }

                return ApiFail<UpdateUserStatusResponse>("更新用户状态失败", result.Errors?.ToArray() ?? [], 400);
            }

            return ApiSuccess(result, "更新用户状态成功");
        }

        /// <summary>
        /// 暂停用户
        /// </summary>
        [HttpPatch("{id}/suspend")]
        [RequiresPermission(PermissionConstants.EditUsers)]
        [ProducesResponseType(typeof(ApiResponse<SuspendUserResponse>), 200)]
        [ProducesResponseType(typeof(ApiResponse), 400)]
        [ProducesResponseType(typeof(ApiResponse), 404)]
        public async Task<ActionResult<ApiResponse<SuspendUserResponse>>> SuspendUser(
            string id,
            [FromBody] SuspendUserCommand command)
        {
            var suspendCommand = command with { UserId = id };
            var result = await Mediator.Send(suspendCommand);

            if (!result.IsSuccessful)
            {
                if (result.ErrorCode == "USER_NOT_FOUND")
                {
                    return ApiFail<SuspendUserResponse>("暂停用户失败", result.Errors?.ToArray() ?? UserNotFoundError, 404);
                }

                return ApiFail<SuspendUserResponse>("暂停用户失败", result.Errors?.ToArray() ?? [], 400);
            }

            return ApiSuccess(result, "暂停用户成功");
        }

        /// <summary>
        /// 获取系统用户列表
        /// </summary>
        [HttpGet("system")]
        [RequiresPermission(PermissionConstants.ViewUsers)]
        [ProducesResponseType(typeof(ApiResponse<PaginatedList<GetUserResponse>>), 200)]
        [ProducesResponseType(typeof(ApiResponse), 401)]
        public async Task<ActionResult<ApiResponse<PaginatedList<GetUserResponse>>>> GetSystemUsers(
            [FromQuery] string? keyword = null,
            [FromQuery] string? status = null,
            [FromQuery] DateTime? startDate = null,
            [FromQuery] DateTime? endDate = null,
            [FromQuery] string? sortField = null,
            [FromQuery] string? sortOrder = null,
            [FromQuery] string[]? roles = null,
            [FromQuery] int page = 1,
            [FromQuery] int pageSize = 10)
        {
            var query = new GetSystemUsersQuery
            {
                Keyword = keyword,
                Status = status,
                StartDate = startDate,
                EndDate = endDate,
                SortField = sortField,
                SortOrder = sortOrder,
                SystemRoles = roles,
                Page = page,
                PageSize = pageSize
            };

            var result = await Mediator.Send(query);
            return ApiSuccess(result);
        }

        /// <summary>
        /// 批量更新所有用户的系统用户标识
        /// </summary>
        [HttpPost("update-system-flags")]
        [RequiresPermission(PermissionConstants.EditUsers)]
        [ProducesResponseType(typeof(ApiResponse<UpdateAllUsersSystemFlagResponse>), 200)]
        [ProducesResponseType(typeof(ApiResponse), 400)]
        [ProducesResponseType(typeof(ApiResponse), 401)]
        public async Task<ActionResult<ApiResponse<UpdateAllUsersSystemFlagResponse>>> UpdateAllUsersSystemFlag(
            [FromBody] UpdateAllUsersSystemFlagCommand command)
        {
            try
            {
                var result = await Mediator.Send(command);

                if (!result.IsSuccessful)
                {
                    return ApiFail<UpdateAllUsersSystemFlagResponse>(
                        "批量更新用户系统标识失败",
                        result.Errors.ToArray(),
                        400);
                }

                return ApiSuccess(result, $"批量更新完成，成功更新 {result.UpdatedUsers} 个用户");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "批量更新用户系统标识时发生错误");
                return ApiFail<UpdateAllUsersSystemFlagResponse>(
                    "批量更新用户系统标识失败",
                    new[] { ex.Message },
                    500);
            }
        }
    }
}